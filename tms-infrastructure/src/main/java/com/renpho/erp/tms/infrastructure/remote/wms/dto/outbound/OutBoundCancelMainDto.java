package com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 调拨出库-取消主表 dto
 *
 * <AUTHOR>
 * @since 2025/08/26
 */
@Data
public class OutBoundCancelMainDto {

    /**
     * 销售出库单号集合
     */
    private List<String> outboundNoList;

    public synchronized void addOutboundNo(String outboundNo) {
        if (outboundNoList == null) {
            outboundNoList = new java.util.ArrayList<>();
        }
        outboundNoList.add(outboundNo);
    }

}
