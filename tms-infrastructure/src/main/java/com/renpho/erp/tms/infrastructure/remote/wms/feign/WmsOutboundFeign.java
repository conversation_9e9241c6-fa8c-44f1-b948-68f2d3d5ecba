package com.renpho.erp.tms.infrastructure.remote.wms.feign;

import com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound.OutBoundCancelMainDto;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound.OutboundCreateMainVo;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound.OutboundMainDto;
import com.renpho.karma.dto.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 出库单接口
 * <AUTHOR>
 * @since 2025.8.26
 */
@FeignClient(
        name = "wms",
        url = "${wms.address}"
//        url = "http://************:9050"
)
public interface WmsOutboundFeign {

    /**
     * 新增出库单
     * @return 出库单号
     */
    @PostMapping("/open/transfer/outbound/addWithLabelChange")
    R<OutboundCreateMainVo> addOutboundOrder(@RequestHeader("FROM_FEIGN") String fromIn, @RequestHeader("Authorization") String token, @RequestBody OutboundMainDto cmd);

    /**
     * 取消出库单
     * @return 是否成功
     */
    @PostMapping("/open/transfer/outbound/cancelWithLabelChange")
    R<Boolean> cancelOutboundOrder(@RequestHeader("FROM_FEIGN") String fromIn, @RequestHeader("Authorization") String token, @RequestBody OutBoundCancelMainDto cmd);
}
