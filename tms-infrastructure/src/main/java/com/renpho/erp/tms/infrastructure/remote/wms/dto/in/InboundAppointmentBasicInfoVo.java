package com.renpho.erp.tms.infrastructure.remote.wms.dto.in;

import lombok.Data;

/**
 * 入库预约单返回类
 * <AUTHOR>
 * @since 2025.7.16
 */
@Data
public class InboundAppointmentBasicInfoVo {

    /**
     * 数据源 0-旧ERP 1-新ERP
     */
    private Integer datasource;

    /**
     * 入库预约单号
     */
    private String iaNo;

    /**
     * 入库类型： 1=调拨入库， 2=退货入库， 3=盘点入库
     */
    private Integer type;

    /**
     * 物流跟踪单号
     */
    private String trackingNo;

}
