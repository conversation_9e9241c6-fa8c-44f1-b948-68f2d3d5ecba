package com.renpho.erp.tms.infrastructure.remote.wms.dto.in;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/7
 */
@Data
public class IaInfoQueryDto implements VO {

	private Integer id;

	/**
	 * 入库预约单号
	 */
	private String iaNo;

	/**
	 * 入库预约单关联的调拨单号
	 */
	private String toNo;

	/**
	 * 入库预约单关联的调拨申请号
	 */
	private String trNo;

	/**
	 * 签收时间（签收开始时间）
	 */
	private LocalDateTime receivedTime;

	/**
	 * 上架时间（上架开始时间）
	 */
	private LocalDateTime putAwayTime;

	/**
	 * 入库状态： 1=待收货， 2=签收中， 3=全部签收， 4=已取消
	 */
	@Trans(type = TransType.DICTIONARY, key = "inbound_status", ref = "inboundStatusName")
	private Integer inboundStatus;

	private String inboundStatusName;

	/**
	 * 预约明细
	 */
	private List<IaInfoQueryItemsDto> items;

}
