package com.renpho.erp.tms.infrastructure.remote.wms.dto.in;

import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InboundAppointmentDetailVo extends Command {

    @Serial
    private static final long serialVersionUID = -4321500975560968780L;

    /**
     * PSKU
     */
    @NotBlank(message = "psku.empty.error")
    private String purchaseSku;

    /**
     * PSKU条码
     */
    @NotBlank(message = "barcode.empty.error")
    private String barcode;

    /**
     * 计划数量
     */
    @NotNull(message = "planQty.empty.error")
    private Integer planningQty;

    /**
     * 外箱长
     */
    private BigDecimal length;

    /**
     * 外箱宽
     */
    private BigDecimal width;

    /**
     * 外箱高
     */
    private BigDecimal height;

    /**
     * 箱规名称
     */
    private String name;

    /**
     * 装箱数量, 该箱规对应的数量
     */
    private Integer quantity;

    /**
     * 箱数
     */
    private Integer boxQty;

}
