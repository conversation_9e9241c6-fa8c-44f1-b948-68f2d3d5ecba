package com.renpho.erp.tms.infrastructure.remote.wms.dto.in;

import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Getter
@Setter
public class IaInfoUpdateDto extends Command {

    @Serial
    private static final long serialVersionUID = 6538116525920240840L;

    /**
     * 入库预约单号
     */
    private String iaNo;

    /**
     * 物流跟踪单号
     */
    @Size(max = 90, message = "trackingNo.length.error")
    private String trackingNo;

    /**
     * TO单号
     */
    private String toNo;

}
