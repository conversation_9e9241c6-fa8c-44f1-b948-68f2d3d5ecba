package com.renpho.erp.tms.infrastructure.remote.wms.feign;

import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.*;
import com.renpho.karma.dto.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 入库预约单接口
 * <AUTHOR>
 * @since 2025.7.16
 */
@FeignClient(
        name = "wms",
        url = "${wms.address}"
//        url = "http://************:9050"
)
public interface WmsInboundFeign {

    /**
     * 新增入库预约单
     * @return 结果
     */
    @PostMapping("/open/ia/add")
    R<InboundAppointmentBasicInfoVo> addInboundAppointmentInfo(@RequestHeader("FROM_FEIGN") String fromIn, @RequestHeader("Authorization") String token, @RequestBody InboundAppointmentBasicInfoAddDto cmd);

    /**
     * 更新入库预约单（主要是更新运单号）
     *
     * @param cmd 入库预约信息
     * @return 结果
     */
    @PostMapping("/open/ia/update")
    R<Boolean> update(@RequestHeader("FROM_FEIGN") String fromIn, @RequestHeader("Authorization") String token, @RequestBody IaInfoUpdateDto cmd);

    /**
     * 取消入库预约单
     *
     * @param cmd 入库预约信息
     * @return 结果
     */
    @PostMapping("/open/ia/cancel")
    R<Boolean> cancel(@RequestHeader("FROM_FEIGN") String fromIn, @RequestHeader("Authorization") String token, @RequestBody IaInfoDto cmd);

    /**
     * 查询入库预约单
     *
     * @param cmd 入库预约信息
     * @return 结果
     */
    @PostMapping("/open/ia/selectIaInfo")
    R<IaInfoQueryDto> selectIaInfo(@RequestHeader("FROM_FEIGN") String fromIn, @RequestHeader("Authorization") String token, @RequestBody IaInfoDto cmd);


}
