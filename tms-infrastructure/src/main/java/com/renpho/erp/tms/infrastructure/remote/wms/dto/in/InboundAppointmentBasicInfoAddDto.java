package com.renpho.erp.tms.infrastructure.remote.wms.dto.in;

import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InboundAppointmentBasicInfoAddDto extends Command {

    @Serial
    private static final long serialVersionUID = -919976511876331155L;

    /**
     * 数据源 0-旧ERP 1-新ERP
     */
    @NotNull(message = "datasource.empty.error")
    private Integer datasource;

    /**
     * 入库类型： 1=调拨入库， 2=退货入库， 3=盘点入库
     */
    @NotNull(message = "inbound.type.empty.error")
    private Integer type;

    /**
     * WMS货主ID
     */
    @NotNull(message = "owner.empty.error")
    private Integer ownerId;

    /**
     * 物流跟踪单号
     */
    @Size(max = 90, message = "trackingNo.length.error")
    private String trackingNo;

    /**
     * 货柜编号
     */
    @Size(max = 60, message = "containerNo.length.error")
    private String containerNo;

    /**
     * 预计送达时间
     */
    private LocalDate estimatedDeliveryTime;

    /**
     * 物流负责人
     */
    private String logisticsPic;

    /**
     * 销售负责人
     */
    private String salesPic;

    /**
     * 入库预约单关联的调拨单号
     */
    private String toNo;

    /**
     * 入库预约单关联的调拨申请号
     */
    private String trNo;

    /**
     * 入库预约单关联的采购单号
     */
    private String poNo;

    /**
     * 入库预约单关联的销售单号
     */
    private String soNo;

    /**
     * 备注
     */
    @Size(max = 300, message = "note.length.error")
    private String note;

    /**
     * 预约单产品明细
     */
    private List<InboundAppointmentDetailVo> items;

}
