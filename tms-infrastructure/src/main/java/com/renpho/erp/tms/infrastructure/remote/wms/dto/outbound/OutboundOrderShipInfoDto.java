package com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 调拨出库单-发运信息表 (wms_transfer_outbound_order_ship_info).
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
public class OutboundOrderShipInfoDto {

    /**
     * 货件类型：0-FBA, 1-WFS, 2-FBT, 3-MCL, 4-其他
     */
    @NotNull(message = "{TRANSFER_SHIPMENT_TYPE_NOT_NULL}")
    private Integer shipmentType;

    /**
     * 货件编号
     */
    private String shipmentId;

    /**
     * 是否打托
     */
    private boolean isPallet;

    /**
     * 是否紧急
     */
    private boolean isUrgent;

}

