package com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound;

import com.renpho.karma.timezone.annotation.TimeZoneIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 调拨出库单 (wms_transfer_outbound_order).
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
public class OutboundOrderDto {

    /**
     * 仓库ID
     */
    private Integer warehouseId;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 上游系统推送的调拨单编号
     */
    private String toNo;

    /**
     * 上游系统推送的调拨单编号
     */
    @NotNull(message = "{TRANSFER_TR_NO_NOT_NULL}")
    private String trNo;

    /**
     * 订单类型：0-仓间调拨, 1-返修出库, 2-备货出库, 3-其他出库
     */
    @NotNull(message = "{TRANSFER_TYPE_NOT_NULL}")
    private Integer type;

    /**
     * 销售负责人
     */
    @NotNull(message = "{TRANSFER_SALES_PIC_NOT_NULL}")
    private String salesPic;

    /**
     * 物流负责人
     */
    @NotNull(message = "{TRANSFER_LOGISTICS_PIC_NOT_NULL}")
    private String logisticsPic;

    /**
     * 备注
     */
    private String note;

    /**
     * 期望发货日期
     */
    @TimeZoneIgnore
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectedDeliveryTime;

    /**
     * 外箱链接
     * 1、贴箱唛的给（包含ASN）
     * 2、不贴箱唛的，不用给
     */
    private List<String> boxLabelUrlList;

    // DefaultPO 中包含公共字段
}

