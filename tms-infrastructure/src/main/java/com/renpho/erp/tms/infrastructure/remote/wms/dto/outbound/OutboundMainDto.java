package com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 调拨出库单主入口.
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
public class OutboundMainDto {

    /**
     * 调拨出库单
     */
    @Valid
    @NotNull
    private OutboundOrderDto order;

    /**
     * 调拨出库单-发运信息表
     */
    @Valid
    @NotNull
    private OutboundOrderShipInfoDto orderShipInfo;

    /**
     * 调拨出库单明细表
     */
    @Valid
    @NotNull
    private List<OutboundOrderItemDto> orderItemList;


}
