package com.renpho.erp.tms.application.transferorder.walmart;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetInboundShipmentItemsRequest;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetInboundShipmentItemsResponse;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsRequest;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsResponse;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.tms.application.inbound.InboundRecordQueryService;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.inbound.*;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.common.constant.CommonConstant;
import com.renpho.erp.tms.infrastructure.persistence.inbound.po.converter.InboundRecordConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.ShopAccountBuilder;
import com.renpho.erp.tms.infrastructure.remote.apiproxy.inbound.WalmartInboundClient;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import com.renpho.erp.tms.infrastructure.util.DateUtils;
import com.renpho.karma.dto.R;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/8/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransferOrderWalmartInboundService {

    private final TransferOrderQueryService transferOrderQueryService;
    private final WalmartInboundClient walmartInboundClient;
    private final StoreLookup storeLookup;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final InboundRecordRepository inboundRecordRepository;
    private final InboundRecordQueryService inboundRecordQueryService;
    private final InboundRecordConverter inboundRecordConverter;
    private final PushTaskService pushTaskService;
    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;

    public void pullShipmentsFromWalmartForTS(List<String> tsNos, List<TransferOrderId> tsIds, String syncApiStatus) {
        //“已发货”和“部分签收”状态的WFS TS单
        List<TransferOrder> wfsTsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.WFS,
                tsIds,
                tsNos,
                List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING),
                syncApiStatus
        ).stream().filter(ts -> StringUtil.isNotBlank(ts.getShipmentId())).toList();

        //将wfsTrs过滤渠道=walmart，按照Store进行分组
        Map<Store, List<TransferOrder>> shipmentsGroupByStore = CollectionUtil.emptyIfNull(wfsTsList).stream()
                .collect(Collectors.groupingBy(TransferOrder::getStore));

        for (Map.Entry<Store, List<TransferOrder>> entry : shipmentsGroupByStore.entrySet()) {
            List<TransferOrder> trs = entry.getValue();
            Store store = entry.getKey();
            StoreAuthorizationVo authorization = storeLookup.getAuthorization(store);
            com.renpho.erp.apiproxy.walmart.model.ShopAccount shopAccount = ShopAccountBuilder.buildWalmartShopAccount(authorization);
            //获取入库单详情
            for (TransferOrder ts : trs) {
                fetchAndProcessShipmentDetails(ts, shopAccount);
            }
        }
    }

    /**
     * 拉取WFS入库单详情，并且处理入库数据
     *
     * @param ts
     * @param shopAccount
     */
    private void fetchAndProcessShipmentDetails(TransferOrder ts,
                                                com.renpho.erp.apiproxy.walmart.model.ShopAccount shopAccount) {
        GetShipmentsRequest req = new GetShipmentsRequest();
        req.setShipmentId(ts.getShipmentId());

        // 保存请求记录
        InboundRequestHistory history = inboundRequestHistoryService.add(ts, WarehouseProviderType.WFS, null, req);

        try {
            PushTask inboundPushTask = pushTaskService.inbound(ts, PushTaskStatus.PENDING, 0, null);
            R<GetShipmentsResponse> r = pushTaskService.execute(() -> {
                R<com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsResponse> ret = walmartInboundClient.getShipments(shopAccount, req);
                //将异常抛出才能触发重试机制
                if (!ret.isSuccess()) {
                    log.error("调用【WFS-获取入库单列表】接口异常：, tsNo={}, 异常信息：{}", ts.getTsNo(), ret);
                    inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, ret);
                    throw new RuntimeException("API获取入库单失败，原因：%s".formatted(ret.getMessage()));
                }
                return ret;
            }, inboundPushTask);

            inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_SUCCESS_CODE, r.getData().getHeaders(), r.getData());

            List<com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsResponse.PayloadItem> payload = r.getData().getPayload();
            for (com.renpho.erp.apiproxy.walmart.model.fulfillment.GetShipmentsResponse.PayloadItem shipment : payload) {
                String inboundStatus = shipment.getStatus();

                //非签收中和已完成，不去获取item详情
                if (!Set.of("RECEIVING_IN_PROGRESS", "CLOSED").contains(inboundStatus)) {
                    continue;
                }

                //获取item详情
                SpringUtil.getBean(this.getClass())
                        .handleWalmartShipmentItems(ts, shopAccount, inboundStatus, inboundPushTask);
            }
        } catch (Exception e) {
            log.error("拉取【Walmart入库单】失败, tsNo={}", ts.getTsNo(), e);
        }
    }

    /**
     * 获取Walmart的入库单
     *
     * @param ts              TransportRequest
     * @param shopAccount     shopAccount
     * @param inboundStatus   入库单状态
     * @param inboundPushTask 入库单推送任务
     */
    public void handleWalmartShipmentItems(TransferOrder ts,
                                           com.renpho.erp.apiproxy.walmart.model.ShopAccount shopAccount,
                                           String inboundStatus,
                                           PushTask inboundPushTask) {
        GetInboundShipmentItemsRequest itemsRequest = new GetInboundShipmentItemsRequest();
        itemsRequest.setShipmentId(ts.getShipmentId());

        LocalDateTime triggerTime = LocalDateTime.now();

        //保存请求记录
        InboundRequestHistory history = inboundRequestHistoryService.add(ts, WarehouseProviderType.WFS, null, itemsRequest);
        R<GetInboundShipmentItemsResponse> ret = pushTaskService.execute(() -> {
            R<GetInboundShipmentItemsResponse> itemR = walmartInboundClient.getInboundShipmentItems(shopAccount, itemsRequest);
            //将异常抛出才能触发重试机制
            if (!itemR.isSuccess()) {
                log.error("获取【WFS】入库单详情接口异常：, tsNo={}，异常原因：{}", ts.getTsNo(), itemR.getMessage());
                inboundRequestHistoryService.update(history.getId(), CommonConstant.RESPONSE_FAIL_CODE, null, itemR);
                throw new RuntimeException("API获取入库单失败，原因：%s".formatted(itemR.getMessage()));
            }
            return itemR;
        }, inboundPushTask);

        // 保存请求记录
        InboundRequestHistory itemHistory = inboundRequestHistoryService.add(ts, WarehouseProviderType.WFS, null,
                itemsRequest, CommonConstant.RESPONSE_SUCCESS_CODE, null, ret.getData());


        List<GetInboundShipmentItemsResponse.PayloadItem> itemList = ret.getData().getPayload();
        SpringUtil.getBean(this.getClass()).handleShipmentItems(itemList, inboundStatus, ts, triggerTime, itemHistory.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleShipmentItems(List<GetInboundShipmentItemsResponse.PayloadItem> itemList,
                                    String inboundStatus,
                                    TransferOrder ts,
                                    LocalDateTime triggerTime,
                                    InboundRequestHistoryId requestHistoryId) {
        if (CollectionUtils.isEmpty(itemList)) {
            log.warn("分页的ShipmentItems为空");
            return;
        }

        //将itemData按照gtin进行分组，返回一个Map，key为gtin，value为 GetInboundShipmentItemsResponse.PayloadItem
        Map<String, GetInboundShipmentItemsResponse.PayloadItem> wfsDataMap = itemList.stream().collect(
                Collectors.toMap(
                        GetInboundShipmentItemsResponse.PayloadItem::getGtin,
                        Function.identity(),
                        (v, v2) -> v
                )
        );

        for (TransferOrderItem tsItem : ts.getItems()) {
            if (!wfsDataMap.containsKey(tsItem.getFnSku())) {
                continue;
            }
            GetInboundShipmentItemsResponse.PayloadItem shipmentItem = wfsDataMap.get(tsItem.getFnSku());

            // 查询上一次记录，用于差异计算
            InboundRecord lastRecord = inboundRecordQueryService.findLastByBiz(tsItem).orElse(null);

            processItem(shipmentItem, inboundStatus, tsItem, triggerTime, requestHistoryId, lastRecord);
        }

        //处理完签收逻辑后，处理 TS 和 TS Item 的更新
        transferOrderItemRepository.batchUpdate(ts.getItems());


        if ("RECEIVING_IN_PROGRESS".equals(inboundStatus)) {
            ts.setStatus(TransferOrderStatus.RECEIVING);
            //todo TS单状态记录
        }
        // 若货件状态为 CLOSED，说明已完成签收与上架
        if ("CLOSED".equals(inboundStatus)) {
            ts.setStatus(TransferOrderStatus.RECEIVED);
            //todo TS单状态记录
        }

        ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
        //更新TS单
        transferOrderRepository.updateById(ts);
    }

    private void processItem(GetInboundShipmentItemsResponse.PayloadItem item,
                             String inboundStatus,
                             TransferOrderItem tsItem,
                             LocalDateTime triggerTime,
                             InboundRequestHistoryId requestHistoryId,
                             InboundRecord lastRecord) {

        // 构建新的入库记录对象
        InboundRecord record = inboundRecordConverter.transferOrderItemToInboundRecord(tsItem);
        record.setWarehouseType(WarehouseProviderType.WFS);
        record.setRequestHistoryId(requestHistoryId);

        record.setReceivedTime(triggerTime);
        boolean isFirstReceived = tsItem.checkIsFirstReceived();
        if (isFirstReceived) {
            //设置TR单的开始签收时间
            String isoReceivingStartDateStr = item.getReceivingStartDate();
            if (StringUtil.isNotBlank(isoReceivingStartDateStr)) {

                LocalDateTime receivedTime = DateUtils.toUtcLocalDateTime(isoReceivingStartDateStr);
                tsItem.setReceivedStartTime(receivedTime);
            }
        }


        boolean haveNewReceived = record.buildReceivedInfo(tsItem.getQty(), lastRecord);
        record.setStatus(InboundStatus.FINISH);
        int putawayQty = item.getReceivedUnitsAtFc();
        record.setPutawayTime(triggerTime);

        //本次上架的不良品数量=上架的不良品总数量-历史上架的不良品总数量
        int badQty = item.getDamagedUnitsAtFc() - Optional.ofNullable(lastRecord).map(InboundRecord::getReceivedBadQuantityTotal).orElse(0);
        //上架总数量-不良品总数量=总的良品数量
        //总的良品数量-历史上架的良品总数量=本次上架的良品数量
        int goodQty =
                putawayQty - item.getDamagedUnitsAtFc() - Optional.ofNullable(lastRecord).map(InboundRecord::getReceivedGoodQuantityTotal).orElse(0);
        boolean haveNewPutaway = record.buildPutawayInfo(putawayQty, goodQty, badQty, lastRecord);

        // 更新 TS Item 的签收和上架数量
        updateItemQuantities(tsItem, tsItem.getQty(), putawayQty);

        if ("RECEIVING_IN_PROGRESS".equals(inboundStatus)) {
            record.setPutawayStatus(InboundStatus.WORKING);
        } else if ("CLOSED".equals(inboundStatus)) {
            record.setPutawayStatus(InboundStatus.FINISH);
            tsItem.calculateTheDifferenceValue(tsItem.getReceivedQty(), tsItem.getPutawayQty());
            tsItem.setReceivedEndTime(triggerTime);
        }

        //存在增量数据
        if (haveNewReceived || haveNewPutaway) {
            //保存签收、上架数据
            inboundRecordRepository.add(record);
        }
    }

    /**
     * 更新 TS Item 的签收和上架数量。
     */
    private void updateItemQuantities(TransferOrderItem tsItem, Integer receivedQty, Integer putawayQty) {
        tsItem.setReceivedQty(receivedQty);
        tsItem.setPutawayQty(putawayQty);
    }
}
