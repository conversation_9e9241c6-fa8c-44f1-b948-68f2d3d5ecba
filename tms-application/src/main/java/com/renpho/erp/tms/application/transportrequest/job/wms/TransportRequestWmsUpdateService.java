package com.renpho.erp.tms.application.transportrequest.job.wms;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoUpdateDto;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsInboundFeign;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为WMS-更新入库单任务.
 *
 * <AUTHOR>
 * @since 2025/7/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestWmsUpdateService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;

    private final TransportRequestOrderRepository transportRequestOrderRepository;

    private final WmsInboundFeign wmsInboundFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    public void test() {
//        //createInboundWmsTask();
//        //doingInboundWms(null);
//        DingTalkWarning err =new DingTalkWarning("API更新入库单失败，原因：%s".formatted("测试格式化"));
//        log.info(err.getMessage());
//    }

    /**
     * TR-目的仓为WMS-更新入库单任务.
     */
    @Lock4j(name = "transport:request:wms:inbound:update:basic")
    //@Transactional(rollbackFor = Exception.class)
    public void createInboundWmsTask(List<String> trNoList) {
        try {
            log.info("TR-目的仓为WMS的定时器任务开始");

            // 状态是已离港
            List<TransportOrderStatusEnum> shipStatusList = List.of(TransportOrderStatusEnum.DEPARTED);
            List<TransportRequestOrderData> trDataList = transportRequestQueryService.selectTrListByShipStatusList(shipStatusList, warehouseType);

            // 外部参数
            trDataList = transportRequestCommonService.fillExterWithTrShipStatus(shipStatusList, trDataList, trNoList);

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为WMS-更新入库单任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.updateInbound(tr, PushTaskStatus.PENDING, 0, null);

                        // 执行更新任务
                        pushTaskService.execute(() -> this.update(tr, trPushTask), trPushTask);
                    } catch (Exception e) {
                        log.error("TR-目的仓为WMS的定时器任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为WMS的定时器任务异常", e);
        }
    }

    private Boolean update(TransportRequest tr, PushTask trPushTask){
        String token = transportRequestCommonService.getWmsAuthToken(tr.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        IaInfoUpdateDto iaInfoUpdateVo = new IaInfoUpdateDto();
        iaInfoUpdateVo.setIaNo(tr.getShipmentId());
        iaInfoUpdateVo.setToNo(tr.getToNo());
        iaInfoUpdateVo.setTrackingNo(tr.getTrackingNo());

        log.info("TR-目的仓为WMS-更新任务-请求, trId={}, token={}, request={}", tr.getId(), token, JSON.toJSONString(iaInfoUpdateVo));
        R<Boolean> result = wmsInboundFeign.update(fromIn, token, iaInfoUpdateVo);
        log.info("TR-目的仓为WMS-更新任务-请求, 响应={}", JSON.toJSONString(result));
        if (result != null) {
            if (result.isSuccess()) {
                // 记录请求历史
                inboundRequestHistoryService.add(tr, WarehouseProviderType.WMS, null, iaInfoUpdateVo, 200, null, result);

                // 维护同步状态,更新比较特殊，不是成功而是PENDING
                tr.setSyncApiStatus(SyncApiStatus.PENDING);
                transportRequestOrderRepository.updateById(tr);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(tr, WarehouseProviderType.WMS, null, iaInfoUpdateVo, 200, null, result);
                throw new DingTalkWarning("API更新入库单失败，原因：%s".formatted( result.getMessage()));
            }
        }
        return true;
    }

}
