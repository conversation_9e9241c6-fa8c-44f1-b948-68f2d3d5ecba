package com.renpho.erp.tms.application.transferorder.job.wms;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;

import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistory;
import com.renpho.erp.tms.domain.inbound.InboundStatus;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;

import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;

import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoDto;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoQueryDto;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoQueryItemsDto;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsInboundFeign;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为WMS-入库单签收任务.
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderInWmsDeliveryService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;

    private final WmsInboundFeign wmsInboundFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    @Transactional(rollbackFor = Exception.class)
//    public void test() {
////        createInboundWmsTask(List.of("TR2506190001"));
//    }

    /**
     * TS-目的仓为WMS-入库单签收任务.
     */
    @Lock4j(name = "transfer:request:wms:inbound:delivery:basic")
    //@Transactional(rollbackFor = Exception.class)
    public void createInboundWmsTask(List<String> trNoList) {
        try {
            log.info("TS-目的仓为WMS的定时器任务开始");

            // 状态是部分签收、已发货
            List<TransferOrderStatus> statusList = List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING);
            List<TransferOrderData> tsDataList = transferOrderService.selectTsListByStatusList(statusList, warehouseType);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(statusList, tsDataList, trNoList);

            // 生成入库单推送任务
            if (tsDataList != null && !tsDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = tsMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为WMS-入库单签收任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.inbound(ts, PushTaskStatus.PENDING,0,null);

                        // 执行签收
                        pushTaskService.execute(() -> this.preHandlerDelivery(ts,trPushTask),trPushTask);
                    } catch (Exception e) {
                        log.error("TS-目的仓为WMS-入库单签收任务异常, 入库单推送任务生成失败, tsId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TS-目的仓为WMS-入库单签收任务", e);
        }
    }

    /**
     * 处理签收-前置
     */
    private Boolean preHandlerDelivery(TransferOrder ts, PushTask tsPushTask) {
        // 验证shipment Id是否为空
        if(StringUtils.isBlank(ts.getShipmentId())){
            log.error("TS-目的仓为WMS-入库单签收任务异常, shipment Id为空, tsId={}", ts.getId());
            throw new RuntimeException("TS-目的仓为WMS-入库单签收任务异常, shipment Id为空, tsId=" + ts.getId());
        }

        String token = transferOrderCommonService.getWmsAuthToken(ts.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        IaInfoDto queryVo = new IaInfoDto();
        queryVo.setIaNo(ts.getShipmentId());
        log.info("TS-目的仓为WMS-执行入库单签收任务,请求= {}", JSON.toJSONString(queryVo));
        R<IaInfoQueryDto> result = wmsInboundFeign.selectIaInfo(fromIn, token, queryVo);
        log.info("TS-目的仓为WMS-执行入库单签收任务,响应= {}", JSON.toJSONString(result));
        if (result != null) {
            if (result.isSuccess()) {
                // 记录请求历史
                InboundRequestHistory hisRecord = inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, queryVo, 200, null, result);

                // 签收处理
                SpringUtil.getBean(this.getClass()).handlerDelivery(result, ts, tsPushTask, hisRecord);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, queryVo, 200, null, result);
                throw new DingTalkWarning("API获取入库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return true;
    }

    /**
     * 处理签收
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlerDelivery(R<IaInfoQueryDto> result, TransferOrder ts, PushTask trPushTask, InboundRequestHistory hisRecord) {
        List<TransferOrderItem> tsItems = ts.getItems();
        IaInfoQueryDto iaInfoQueryDto = result.getData();
        List<IaInfoQueryItemsDto> items = result.getData().getItems();
        switch (result.getData().getInboundStatus()) {
            case 2:
                // 签收中
                partSignIn(tsItems, iaInfoQueryDto, items, ts, hisRecord, InboundStatus.WORKING);
                transferOrderRepository.updateById(ts);
                transferOrderItemRepository.update(ts);
                break;
            case 3:
                // 全部签收
                partSignIn(tsItems, iaInfoQueryDto, items, ts, hisRecord, InboundStatus.FINISH);

                // 所有差异数量为0，则调整为已签收
                boolean allFinish = true;
                for(TransferOrderItem tsOrderItem : tsItems){
                    if(!tsOrderItem.getReceivedQty().equals(tsOrderItem.getPutawayQty())){
                        allFinish = false;
                        break;
                    }
                }
                if(allFinish){
                    ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
                    ts.setStatus(TransferOrderStatus.RECEIVED);
                    transferOrderRepository.updateById(ts);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 部分签收
     * <br/> 目前签收策略： 只认同一个shipmentId就表示是期望的sku，不再校验返回的sku跟tr的sku是不是一个东西
     * <br/> 目前的入库单细节：一个TR只会绑定一个sku，不会绑定多个，所以返回的数组只有一个元素
     */
    private void partSignIn(List<TransferOrderItem> tsItems, IaInfoQueryDto iaInfoQueryDto, List<IaInfoQueryItemsDto> items, TransferOrder ts, InboundRequestHistory hisRecord, InboundStatus inboundStatus){
        for(IaInfoQueryItemsDto item : items){
            for(TransferOrderItem transferOrderItem : tsItems){
                if(transferOrderItem.getProduct().getPsku().equals(item.getPsku())){
                    partSignIning(transferOrderItem, iaInfoQueryDto, item, ts, hisRecord, inboundStatus);
                    if(InboundStatus.FINISH==inboundStatus){
                        puyAwayFinished(transferOrderItem, item, ts, iaInfoQueryDto);
                    }
                    break;
                }
            }
        }
    }

    /**
     * 部分签收-执行
     * <br/> 目前签收策略： 只认同一个shipmentId就表示是期望的sku，不再校验返回的sku跟tr的sku是不是一个东西
     * <br/> 目前的入库单细节：一个TR只会绑定一个sku，不会绑定多个，所以返回的数组只有一个元素
     */
    private void partSignIning(TransferOrderItem tsItem, IaInfoQueryDto iaInfoQueryDto, IaInfoQueryItemsDto item, TransferOrder ts, InboundRequestHistory hisRecord, InboundStatus inboundStatus){
        LocalDateTime receivedTime = iaInfoQueryDto.getReceivedTime();
        LocalDateTime putAwayTime = iaInfoQueryDto.getPutAwayTime();

        InboundStatus putAwayStatus = InboundStatus.WORKING;
        Integer receivedGoodQty = 0;
        Integer receivedBadQty = 0;
        Integer putawayGoodQty = 0;
        Integer putawayBadQty = 0;

        // 首次签收推送签收消息到FMS
        // transportRequestService.firstReceiveSendReceiveMsgToFms(ts.getTsNo(), tsItem.getReceivedTime() == null);

        // 签收数量取 TR表的计划数量，即产品数量
        Integer receivedQty = ts.getQty();
        tsItem.setReceivedQty(receivedQty);
        tsItem.setPutawayQty(item.getPutawayQty());
        tsItem.setReceivedStartTime(receivedTime);
        tsItem.setReceivedEndTime(receivedTime);

        // 入库单状态签收中的，则TR单出运状态由“已发货”更新为“部分签收”
        if (TransferOrderStatus.SHIPPED == ts.getStatus()) {
            ts.setStatus(TransferOrderStatus.RECEIVING);
        }

        // 需要调整逻辑，上架数量 = 签收完成数据，才算真正的上架完成
        Integer putAwayQty = tsItem.getPutawayQty();
        if(putAwayQty==null){
            putAwayQty = 0;
        }
        receivedGoodQty = receivedQty; // 等于tr计划数量
        receivedBadQty = 0;  // 签收默认都是良品，所以不良品是0
        putawayGoodQty = item.getPutawayGoodQty();
        putawayBadQty = item.getPutawayBadQty();
        if(receivedQty.equals(putAwayQty)){
            putAwayStatus = InboundStatus.FINISH;
        }

        // 记录record
        transferOrderCommonService.doRecord(warehouseType, receivedQty, receivedGoodQty, receivedBadQty, putAwayQty, putawayGoodQty, putawayBadQty, receivedTime, putAwayTime, tsItem, hisRecord, inboundStatus, putAwayStatus);
    }

    /**
     * 上架签收完成处理
     */
    private void puyAwayFinished(TransferOrderItem tsItem, IaInfoQueryItemsDto item, TransferOrder ts, IaInfoQueryDto iaInfoQueryDto) {
        // 计算签收差异数， wms比较特殊，要用实际签收数量减去计划数量，才是差异数量
        Integer diffQuantity = item.getReceivedQty() - ts.getQty();
        tsItem.setReceivedDiscrepancy(diffQuantity);

        // 上架差异数量,目前不计算，目前方案都是0
        tsItem.setPutawayDiscrepancy(item.getPutawayQty() - ts.getQty());
        transferOrderItemRepository.update(ts);

//        // 需要调整逻辑，上架数量 = 签收完成数据，才算真正的上架完成
//        Integer receiveQty = tsItem.getReceivedQty();
//        Integer putAwayQty = tsItem.getPutawayQty();
//        if(receiveQty.equals(putAwayQty)){
//            // 同步TR单状态，有差异数量已签收，没有差异数量已完成
//             transportRequestCommonService.syncTransportRequestStatus(diffQuantity, ts);
//        }
    }

}
