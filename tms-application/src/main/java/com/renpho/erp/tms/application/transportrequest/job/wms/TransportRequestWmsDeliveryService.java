package com.renpho.erp.tms.application.transportrequest.job.wms;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;

import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;

import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistory;
import com.renpho.erp.tms.domain.inbound.InboundStatus;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.TransportOrderStatusEnum;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderItemRepository;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoDto;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoQueryDto;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoQueryItemsDto;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsInboundFeign;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为WMS-入库单签收任务.
 *
 * <AUTHOR>
 * @since 2025/7/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestWmsDeliveryService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;
    private final TransportRequestService transportRequestService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderItemRepository transportRequestOrderItemRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    private final WmsInboundFeign wmsInboundFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    @Transactional(rollbackFor = Exception.class)
//    public void test() {
////        createInboundWmsTask(List.of("TR2506190001"));
//    }

    /**
     * TR-目的仓为WMS-入库单签收任务.
     */
    @Lock4j(name = "transport:request:wms:inbound:delivery:basic")
    //@Transactional(rollbackFor = Exception.class)
    public void createInboundWmsTask(List<String> trNoList) {
        try {
            log.info("TR-目的仓为WMS的定时器任务开始");

            // 状态是部分签收、已派送
            List<TransportOrderStatusEnum> shipStatusList = List.of(TransportOrderStatusEnum.DELIVERED_PART, TransportOrderStatusEnum.OUT_FOR_DELIVERY);
            List<TransportRequestOrderData> trDataList = transportRequestCommonService.selectTrDeliveredListByStatus(shipStatusList, warehouseType);

            // 外部参数
            trDataList = transportRequestCommonService.fillExterWithTrShipStatus(shipStatusList, trDataList, trNoList);

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为WMS-入库单签收任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.inbound(tr, PushTaskStatus.PENDING,0,null);

                        // 执行签收
                        pushTaskService.execute(() -> this.preHandlerDelivery(tr,trPushTask),trPushTask);
                    } catch (Exception e) {
                        log.error("TR-目的仓为WMS-入库单签收任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为WMS-入库单签收任务", e);
        }
    }

    /**
     * 处理签收-前置
     */
    private Boolean preHandlerDelivery(TransportRequest tr, PushTask trPushTask) {
        // 验证shipment Id是否为空
        if(StringUtils.isBlank(tr.getShipmentId())){
            log.error("TR-目的仓为WMS-入库单签收任务异常, shipment Id为空, trId={}", tr.getId());
            throw new RuntimeException("TR-目的仓为WMS-入库单签收任务异常, shipment Id为空, trId=" + tr.getId());
        }

        String token = transportRequestCommonService.getWmsAuthToken(tr.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        IaInfoDto queryVo = new IaInfoDto();
        queryVo.setIaNo(tr.getShipmentId());
        log.info("TR-目的仓为WMS-执行入库单签收任务,请求= {}", JSON.toJSONString(queryVo));
        R<IaInfoQueryDto> result = wmsInboundFeign.selectIaInfo(fromIn, token, queryVo);
        log.info("TR-目的仓为WMS-执行入库单签收任务,响应= {}", JSON.toJSONString(result));
        if (result != null) {
            if (result.isSuccess()) {
                // 记录请求历史
                InboundRequestHistory hisRecord = inboundRequestHistoryService.add(tr, WarehouseProviderType.WMS, null, queryVo, 200, null, result);

                // 签收处理
                SpringUtil.getBean(this.getClass()).handlerDelivery(result, tr, trPushTask, hisRecord);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(tr, WarehouseProviderType.WMS, null, queryVo, 200, null, result);
                throw new DingTalkWarning("API获取入库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return true;
    }

    /**
     * 处理签收
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlerDelivery(R<IaInfoQueryDto> result, TransportRequest tr, PushTask trPushTask, InboundRequestHistory hisRecord) {
        TransportRequestItem itemTr = tr.getItem();
        IaInfoQueryDto iaInfoQueryDto = result.getData();
        List<IaInfoQueryItemsDto> items = result.getData().getItems();
        switch (result.getData().getInboundStatus()) {
            case 2:
                // 签收中
                partSignIn(itemTr, iaInfoQueryDto, items, tr, hisRecord, InboundStatus.WORKING);
                transportRequestOrderRepository.updateById(tr);
                transportRequestOrderItemRepository.update(tr);
                break;
            case 3:
                // 全部签收
                partSignIn(itemTr, iaInfoQueryDto, items, tr, hisRecord, InboundStatus.FINISH);

                // 计算签收差异数， wms比较特殊，要用实际签收数量减去计划数量，才是差异数量
                IaInfoQueryItemsDto item = items.get(0);
                Integer diffQuantity = item.getReceivedQty() - tr.getQuantity();
                itemTr.setReceiptDiscrepancy(diffQuantity);

                // 上架差异数量,目前不计算，目前方案都是0
                // itemTr.setShelvedDiscrepancy(itemTr.getShelvedQuantity() - tr.getQuantity());

                tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
                transportRequestOrderRepository.updateById(tr);
                transportRequestOrderItemRepository.update(tr);

                // 需要调整逻辑，上架数量 = 签收完成数据，才算真正的上架完成
                Integer receiveQty = itemTr.getReceivedQuantity();
                Integer putAwayQty = itemTr.getShelvedQuantity();
                if(receiveQty.equals(putAwayQty)){
                    // 同步TR单状态，有差异数量已签收，没有差异数量已完成
                    transportRequestCommonService.syncTransportRequestStatus(diffQuantity, tr);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 部分签收
     * <br/> 目前签收策略： 只认同一个shipmentId就表示是期望的sku，不再校验返回的sku跟tr的sku是不是一个东西
     * <br/> 目前的入库单细节：一个TR只会绑定一个sku，不会绑定多个，所以返回的数组只有一个元素
     */
    private void partSignIn(TransportRequestItem itemTr, IaInfoQueryDto iaInfoQueryDto, List<IaInfoQueryItemsDto> items, TransportRequest tr, InboundRequestHistory hisRecord, InboundStatus inboundStatus){
        LocalDateTime receivedTime = iaInfoQueryDto.getReceivedTime();
        LocalDateTime putAwayTime = iaInfoQueryDto.getPutAwayTime();

        InboundStatus putAwayStatus = InboundStatus.WORKING;
        Integer receivedGoodQty = 0;
        Integer receivedBadQty = 0;
        Integer putawayGoodQty = 0;
        Integer putawayBadQty = 0;

        IaInfoQueryItemsDto item = items.get(0);
        if(items.size()>1){
            throw new DingTalkWarning("API获取入库单失败，原因：%s".formatted("一个TR只能绑定一个sku,trId = {}" + tr.getTrNo()));
        }

        // 首次签收推送签收消息到FMS
        transportRequestService.firstReceiveSendReceiveMsgToFms(tr.getToNo(), tr.getReceivedTime() == null);

        // 签收数量取 TR表的计划数量，即产品数量
        Integer receivedQty = tr.getQuantity();
        itemTr.setReceivedQuantity(receivedQty);
        itemTr.setShelvedQuantity(item.getPutawayQty());
        tr.setReceivedTime(receivedTime);
        tr.setReceivedEndTime(receivedTime);

        // 入库单状态签收中的，则TR单出运状态由“已派送”更新为“部分签收”
        if (TransportOrderStatusEnum.OUT_FOR_DELIVERY == tr.getShipStatus()) {
            tr.setShipStatus(TransportOrderStatusEnum.DELIVERED_PART);
        }

        // 需要调整逻辑，上架数量 = 签收完成数据，才算真正的上架完成
        Integer putAwayQty = itemTr.getShelvedQuantity();
        if(putAwayQty==null){
            putAwayQty = 0;
        }
        receivedGoodQty = receivedQty; // 等于tr计划数量
        receivedBadQty = 0;  // 签收默认都是良品，所以不良品是0
        putawayGoodQty = item.getPutawayGoodQty();
        putawayBadQty = item.getPutawayBadQty();
        if(receivedQty.equals(putAwayQty)){
            putAwayStatus = InboundStatus.FINISH;
        }

        // 记录record + 分摊计算
        transportRequestCommonService.doRecord(warehouseType, receivedQty, receivedGoodQty, receivedBadQty, putAwayQty, putawayGoodQty, putawayBadQty, receivedTime, putAwayTime, tr, hisRecord, inboundStatus, putAwayStatus);
    }

}
