package com.renpho.erp.tms.application.transferorder.job.wms;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.InboundAppointmentBasicInfoAddDto;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.InboundAppointmentBasicInfoVo;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.InboundAppointmentDetailVo;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsInboundFeign;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为WMS-创建入库单任务.
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderInWmsAddService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;

    private final WmsInboundFeign wmsInboundFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    public void test() {
//        //createInboundWmsTask(null);
//        //doingPalletWms(null);
//    }

    /**
     * TS-目的仓为WMS-创建入库单任务.
     */
    @Lock4j(name = "transfer:request:wms:inbound:add:basic")
    // @Transactional(rollbackFor = Exception.class)
    public void createInboundWmsTask(List<String> trNoList) {
        try {
            log.info("TR-目的仓为WMS的定时器任务开始");

            // 状态是待创建入库单
            TransferOrderStatus status = TransferOrderStatus.WAIT_CREATE_INBOUND;
            List<TransferOrderData> tsDataList = transferOrderCommonService.selectTsListByStatus(status.getValue(), warehouseType);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(status, tsDataList, trNoList);

            // 生成入库单推送任务
            if (tsDataList != null && !tsDataList.isEmpty()) {
                // 获取ts上下文信息
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = tsMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为WMS-创建入库单任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (WarehouseProviderType.WMS != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.createInbound(ts, PushTaskStatus.PENDING,0,null);

                        // 执行创建入库单
                        pushTaskService.execute( () -> deliveryInstock(ts, trPushTask), trPushTask);
                    } catch (Exception e) {
                        log.error("TR-目的仓为WMS的定时器任务异常, 入库单推送任务生成失败, trId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为WMS的定时器任务异常", e);
        }
    }

    /**
     * 执行入库单创建
     *
     * @param ts         ts上下文
     * @param trPushTask 入库预约单推送任务
     */
    private Boolean deliveryInstock(TransferOrder ts, PushTask trPushTask) {
        String token = transferOrderCommonService.getWmsAuthToken(ts.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        List<TransferOrderItem> tsItems = ts.getItems();
        InboundAppointmentBasicInfoAddDto inboundAdd = new InboundAppointmentBasicInfoAddDto();
        inboundAdd.setDatasource(1);  // 新ERP
        inboundAdd.setType(1);  // 调拨入库
        inboundAdd.setOwnerId(ts.getOwner().getId().id());
        inboundAdd.setTrackingNo(ts.getShipmentId());
        inboundAdd.setToNo(ts.getTsNo());  // 创建入库单的时候先用tr单号作为to的值，然后更新接口加一个to字段
        inboundAdd.setTrNo(ts.getTsNo());
        inboundAdd.setPoNo(ts.getOrderNo());

        List<InboundAppointmentDetailVo> items = new ArrayList<>();
        for(TransferOrderItem tsItem : tsItems){
            InboundAppointmentDetailVo item = new InboundAppointmentDetailVo();
            item.setPurchaseSku(tsItem.getProduct().getPsku());
            item.setBarcode(tsItem.getProduct().getFnSku());
            item.setPlanningQty(ts.getQty());
            item.setQuantity(tsItem.getQuantityPerBox());
            item.setBoxQty(tsItem.getBoxQty().intValue());
            items.add(item);
        }
        inboundAdd.setItems(items);

        log.info("TS-目的仓为WMS-执行入库单, 参数={}", JSON.toJSONString(inboundAdd));
        R<InboundAppointmentBasicInfoVo> result = wmsInboundFeign.addInboundAppointmentInfo(fromIn, token, inboundAdd);
        log.info("TS-目的仓为WMS-执行入库单, 响应={}", JSON.toJSONString(result));
        Integer resultCode = 200;
        Object bodyData = null;
        if (result != null) {
            bodyData = result.getData();
            if (result.isSuccess()) {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, inboundAdd, resultCode, null, result);

                // 创建成功，保存 iaNo
                ts.setShipmentId(result.getData().getIaNo());
                ts.setSyncApiStatus(SyncApiStatus.SUCCESS);
                transferOrderRepository.updateById(ts);

                // 3. 生成箱唛推送任务(这个跟tr生命周期重复，所以注释掉)
                // transportRequestCommonService.createCartonFile(tr, warehouseType);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, inboundAdd,200, null, result);
                throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return true;
    }

    /**
     * 执行: TS-目的仓为WMS-箱唛任务.
     * @param tsNoList ts单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transfer:request:wms:inbound:add:pallet")
    @Transactional(rollbackFor = Exception.class)
    public void doingPalletWms(List<String> tsNoList) {
        transferOrderCommonService.doingPallet(warehouseType, tsNoList);
    }

}
