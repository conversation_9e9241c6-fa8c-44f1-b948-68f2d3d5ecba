package com.renpho.erp.tms.application.transferorder.job.wms;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;

import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderData;
import com.renpho.erp.tms.domain.transferorder.TransferOrderRepository;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatus;

import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;

import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoUpdateDto;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsInboundFeign;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为WMS-更新入库单任务.
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderInWmsUpdateService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;

    private final WmsInboundFeign wmsInboundFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    public void test() {
//        //createInboundWmsTask();
//        //doingInboundWms(null);
//        DingTalkWarning err =new DingTalkWarning("API更新入库单失败，原因：%s".formatted("测试格式化"));
//        log.info(err.getMessage());
//    }

    /**
     * TS-目的仓为WMS-更新入库单任务.
     */
    @Lock4j(name = "transfer:request:wms:inbound:update:basic")
    //@Transactional(rollbackFor = Exception.class)
    public void createInboundWmsTask(List<String> trNoList) {
        try {
            log.info("TS-目的仓为WMS的定时器任务开始");

            // 状态是已发货
            List<TransferOrderStatus> statusList = List.of(TransferOrderStatus.SHIPPED);
            List<TransferOrderData> tsDataList = transferOrderService.selectTsListByStatusList(statusList, warehouseType);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(statusList, tsDataList, trNoList);

            // 生成入库单推送任务
            if (tsDataList != null && !tsDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransferOrder> trMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = trMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为WMS-更新入库单任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.updateInbound(ts, PushTaskStatus.PENDING, 0, null);

                        // 执行更新任务
                        pushTaskService.execute(() -> this.update(ts, trPushTask), trPushTask);
                    } catch (Exception e) {
                        log.error("TS-目的仓为WMS的定时器任务异常, 入库单推送任务生成失败, tsId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为WMS的定时器任务异常", e);
        }
    }

    private Boolean update(TransferOrder ts, PushTask trPushTask){
        String token = transferOrderCommonService.getWmsAuthToken(ts.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        IaInfoUpdateDto iaInfoUpdateVo = new IaInfoUpdateDto();
        iaInfoUpdateVo.setIaNo(ts.getShipmentId());
        iaInfoUpdateVo.setToNo(ts.getTsNo());
        iaInfoUpdateVo.setTrackingNo(ts.getShipmentId());

        log.info("TS-目的仓为WMS-更新任务-请求, tsId={}, token={}, request={}", ts.getId(), token, JSON.toJSONString(iaInfoUpdateVo));
        R<Boolean> result = wmsInboundFeign.update(fromIn, token, iaInfoUpdateVo);
        log.info("TS-目的仓为WMS-更新任务-请求, 响应={}", JSON.toJSONString(result));
        if (result != null) {
            if (result.isSuccess()) {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, iaInfoUpdateVo, 200, null, result);

                // 维护同步状态,更新比较特殊，不是成功而是PENDING
                transferOrderRepository.updateSyncApiStatusById(ts.getId(), SyncApiStatus.PENDING);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, iaInfoUpdateVo, 200, null, result);
                throw new DingTalkWarning("API更新入库单失败，原因：%s".formatted( result.getMessage()));
            }
        }
        return true;
    }

}
