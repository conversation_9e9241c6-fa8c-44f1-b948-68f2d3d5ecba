package com.renpho.erp.tms.application.transferorder.yunwms;

import com.renpho.erp.tms.application.api.yunwms.YunWmsInboundOrderContext;
import com.renpho.erp.tms.application.api.yunwms.strategy.TransferOrderInboundStrategy;
import com.renpho.erp.tms.application.transferorder.TransferOrderQueryService;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.saleschannel.SalesChannelConstant;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderId;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatus;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TS单云仓入库服务
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransferOrderYunWmsService {

    private final YunWmsInboundOrderContext yunWmsInboundOrderContext;
    private final TransferOrderInboundStrategy transferOrderStrategy;
    private final TransferOrderQueryService transferOrderQueryService;

    /**
     * 为调拨单创建转运单
     */
    public void createTransferInboundForTs(List<String> tsNos, List<TransferOrderId> tsIds, Boolean isAmazon,
                                           String syncApiStatus) {

        //“待创建入库单”状态的TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                tsIds,
                tsNos,
                List.of(TransferOrderStatus.WAIT_CREATE_INBOUND),
                syncApiStatus
        ).stream().filter(ts -> StringUtil.isEmpty(ts.getShipmentId())).toList();

        if (isAmazon) {
            //amazon tr单，走中转逻辑
            List<TransferOrder> amzTsList = tsList.stream()
                    .filter(tr -> SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName()))
                    .toList();
            //创建 转运单（中转，amazon平台TS）
            yunWmsInboundOrderContext.executeCreateTransferInbound(amzTsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
        } else {
            //非Amazon tr单，走一件代发逻辑
            List<TransferOrder> nonAmzTrs = tsList.stream()
                    .filter(tr -> !SalesChannelConstant.AMAZON.equalsIgnoreCase(tr.getSalesChannel().getChannelName()))
                    .toList();
            return;
        }
    }

    /**
     * 获取走极智佳中转的调拨单箱唛
     * @param tsNos 调拨单号列表
     * @param ids 调拨单ID列表
     * @param syncApiStatus 同步状态
     */
    public void getTransferOrderBoxLabelForTs(List<String> tsNos, List<TransferOrderId> ids, String syncApiStatus) {
        //“待创建入库单”状态的TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                ids,
                tsNos,
                List.of(TransferOrderStatus.WAIT_CREATE_INBOUND),
                syncApiStatus
        ).stream().filter(ts -> SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isEmpty(ts.getShipmentId())).toList();

        yunWmsInboundOrderContext.executeGetTransferOrderBoxLabel(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }


    /**
     * 获取走极智佳中转的调拨单入库信息
     * @param tsNos 调拨单号列表
     * @param ids 调拨单ID列表
     * @param syncApiStatus 同步状态
     */
    public void pullTransferOrderForTS(List<String> tsNos, List<TransferOrderId> ids, String syncApiStatus) {
        //获取所有状态为已发货或部分签收的极智佳 TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                ids,
                tsNos,
                List.of(TransferOrderStatus.SHIPPED, TransferOrderStatus.RECEIVING),
                syncApiStatus
        ).stream().filter(ts -> SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isNotBlank(ts.getShipmentId())).toList();

        yunWmsInboundOrderContext.executePullTransferOrder(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }

    /**
     * 取消(走极智佳中转的调拨单的)转运单
     * @param tsNos 调拨单号列表
     * @param ids 调拨单ID列表
     * @param syncApiStatus 同步状态
     */
    public void cancelTransferOrderForTS(List<String> tsNos, List<TransferOrderId> ids, String syncApiStatus) {
        //获取所有状态为已作废的极智佳 TS单
        List<TransferOrder> tsList = transferOrderQueryService.findByWarehouseProviderType(
                WarehouseProviderType.POLARIS_YUNWMS,
                ids,
                tsNos,
                List.of(TransferOrderStatus.CANCEL),
                syncApiStatus
        ).stream().filter(ts -> SalesChannelConstant.AMAZON.equalsIgnoreCase(ts.getSalesChannel().getChannelName())
                && StringUtil.isNotBlank(ts.getShipmentId())).toList();


        yunWmsInboundOrderContext.executeCancelTransferOrder(tsList, transferOrderStrategy, TransferOrder::getDestWarehouse);
    }
}
