package com.renpho.erp.tms.application.transportrequest.job.wms;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;

import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.InboundAppointmentBasicInfoAddDto;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.InboundAppointmentBasicInfoVo;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.InboundAppointmentDetailVo;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsInboundFeign;

import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为WMS-创建入库单任务.
 *
 * <AUTHOR>
 * @since 2025/7/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestWmsAddService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    private final WmsInboundFeign wmsInboundFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    @Transactional(rollbackFor = Exception.class)
//    public void test() {
//        //createInboundWmsTask(null);
//        //doingPalletWms(null);
//    }

    /**
     * TR-目的仓为WMS-创建入库单任务.
     */
    @Lock4j(name = "transport:request:wms:inbound:add:basic")
    // @Transactional(rollbackFor = Exception.class)
    public void createInboundWmsTask(List<String> trNoList) {
        try {
            log.info("TR-目的仓为WMS的定时器任务开始");

            // 状态是待补充信息
            TransportRequestStatus status = TransportRequestStatus.PENDING;
            List<TransportRequestOrderData> trDataList = transportRequestCommonService.selectTrListByStatus(status.getValue(), warehouseType);

            // 外部参数
            trDataList = transportRequestCommonService.fillExterWithTrStatus(status, trDataList, trNoList);

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为WMS-创建入库单任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (WarehouseProviderType.WMS != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.createInbound(tr, PushTaskStatus.PENDING,0,null);

                        // 执行创建入库单
                        pushTaskService.execute( () -> deliveryInstock(tr, trPushTask), trPushTask);
                    } catch (Exception e) {
                        log.error("TR-目的仓为WMS的定时器任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为WMS的定时器任务异常", e);
        }
    }

    /**
     * 执行入库单创建
     *
     * @param tr         tr上下文
     * @param trPushTask 入库预约单推送任务
     */
    private Boolean deliveryInstock(TransportRequest tr, PushTask trPushTask) {
        String token = transportRequestCommonService.getWmsAuthToken(tr.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        TransportRequestItem trItem = tr.getItem();
        InboundAppointmentBasicInfoAddDto inboundAdd = new InboundAppointmentBasicInfoAddDto();
        inboundAdd.setDatasource(1);  // 新ERP
        inboundAdd.setType(1);  // 调拨入库
        inboundAdd.setOwnerId(tr.getOwner().getId().id());
        inboundAdd.setTrackingNo(tr.getTrackingNo());
        inboundAdd.setToNo(tr.getTrNo());  // 创建入库单的时候先用tr单号作为to的值，然后更新接口加一个to字段
        inboundAdd.setTrNo(tr.getTrNo());
        inboundAdd.setPoNo(tr.getPoNo());

        String fnSku = tr.getFnSku();
        log.info("TR-目的仓为WMS-执行入库单, fnSku={}", fnSku);

        List<InboundAppointmentDetailVo> items = new ArrayList<>();
        InboundAppointmentDetailVo item = new InboundAppointmentDetailVo();
        item.setPurchaseSku(trItem.getProduct().getPsku());
        item.setBarcode(trItem.getProduct().getFnSku());
        item.setPlanningQty(tr.getQuantity());
        item.setQuantity(trItem.getQuantityPerBox());
        item.setBoxQty(trItem.getBoxQty().intValue());

        items.add(item);
        inboundAdd.setItems(items);

        log.info("TR-目的仓为WMS-执行入库单, 参数={}", JSON.toJSONString(inboundAdd));
        R<InboundAppointmentBasicInfoVo> result = wmsInboundFeign.addInboundAppointmentInfo(fromIn, token, inboundAdd);
        log.info("TR-目的仓为WMS-执行入库单, 响应={}", JSON.toJSONString(result));
        Integer resultCode = 200;
        Object bodyData = null;
        if (result != null) {
            bodyData = result.getData();
            if (result.isSuccess()) {
                // 记录请求历史
                inboundRequestHistoryService.add(tr, WarehouseProviderType.WMS, null, inboundAdd, resultCode, null, result);

                // 创建成功，保存 iaNo
                tr.setShipmentId(result.getData().getIaNo());
                tr.setSyncApiStatus(SyncApiStatus.SUCCESS);
                transportRequestOrderRepository.updateById(tr);

                // 3. 生成箱唛推送任务(这个跟tr生命周期重复，所以注释掉)
                // transportRequestCommonService.createCartonFile(tr, warehouseType);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(tr, WarehouseProviderType.WMS, null, inboundAdd,200, null, result);
                throw new DingTalkWarning("API创建入库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return true;
    }

    /**
     * 执行: TR-目的仓为WMS-箱唛任务.
     * @param trNoList tr单号集合，用于异常重试的参数触发机制
     */
    @Lock4j(name = "transport:request:wms:inbound:add:pallet")
    @Transactional(rollbackFor = Exception.class)
    public void doingPalletWms(List<String> trNoList) {
        transportRequestCommonService.doingPallet(warehouseType, trNoList);
    }


}
