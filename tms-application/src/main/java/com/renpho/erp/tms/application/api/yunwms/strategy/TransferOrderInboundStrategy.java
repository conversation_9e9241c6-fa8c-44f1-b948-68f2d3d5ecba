package com.renpho.erp.tms.application.api.yunwms.strategy;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.renpho.erp.apiproxy.eccang.yunwms.model.transfer.*;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transportrequest.PushTaskQueryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistory;
import com.renpho.erp.tms.domain.inbound.InboundRequestHistoryId;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.domain.transferorder.TransferOrderRepository;
import com.renpho.erp.tms.domain.transferorder.TransferOrderStatus;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.PushTaskType;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.common.converter.MultiLanguageConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderItemConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * TransferOrder入库单生成策略
 */
@Component
@RequiredArgsConstructor
public class TransferOrderInboundStrategy implements InboundOrderStrategy<TransferOrder> {
    
    private final PushTaskService pushTaskService;
    private final PushTaskQueryService pushTaskQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderService transferOrderService;
    private final TransferOrderItemConverter transferOrderItemConverter;
    private final MultiLanguageConverter multiLanguageConverter;

    @Override
    public TransferOrderSaveRequest buildCreateTransferInboundRequest(TransferOrder order) {
        // TransferOrder的转运单创建逻辑
        TransferOrderSaveRequest request = new TransferOrderSaveRequest();
        request.setAddService(0);
        request.setTransferWay(1);
        request.setWarehouseNo(order.getDestWarehouse().getThirdWarehouseCode());
        request.setReferenceNo(order.getTsNo());
        
        request.setPredictArriveTime(DateUtil.format(order.getEstimatedDeliveryTime(), DatePattern.NORM_DATE_PATTERN));

        // 构建箱子信息和SKU信息
        List<TransferOrderSaveRequest.InboundBoxDTO> inboundBoxList = order.getItems().stream().map(item -> {
            TransferOrderSaveRequest.InboundBoxDTO inboundItem = new TransferOrderSaveRequest.InboundBoxDTO();
            inboundItem.setBoxMark(order.getTsNo());
            inboundItem.setHeight(item.getProduct().getActiveBoxSpec().getBoxHeightMetric().doubleValue());
            inboundItem.setWidth(item.getProduct().getActiveBoxSpec().getBoxWidthMetric().doubleValue());
            inboundItem.setLength(item.getProduct().getActiveBoxSpec().getBoxLengthMetric().doubleValue());
            inboundItem.setSkuDesc(item.getProduct().getPsku());
            inboundItem.setTotalBoxNum(item.getBoxQty().doubleValue());
            inboundItem.setWeight(item.getTotalGrossWeight().doubleValue());

            TransferOrderSaveRequest.InboundSkuDTO skuItem = new TransferOrderSaveRequest.InboundSkuDTO();
            skuItem.setProductCode(item.getProduct().getFnSku());
            String cnName = multiLanguageConverter.findCnName(item.getProduct().getNames());
            skuItem.setProductDesc(cnName);
            skuItem.setSingleNum(item.getQuantityPerBox());
            inboundItem.setSkuList(List.of(skuItem));

            return inboundItem;
        }).toList();

        request.setInboundList(inboundBoxList);
        return request;
    }

    @Override
    public TransferOrderLabelRenderBoxMarkRequest buildBoxLabelRequest(TransferOrder order) {
        TransferOrderLabelRenderBoxMarkRequest req = new TransferOrderLabelRenderBoxMarkRequest();
        req.setTransferNo(order.getShipmentId());
        req.setBoxMarkType(1);
        req.setTemplateId(14721);
        return req;
    }

    @Override
    public PushTask createPushTask(TransferOrder order) {
        // 需要根据TransferOrder创建PushTask的逻辑
        return pushTaskService.createInbound(order, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    public PushTask getCartonFileGenerationTask(TransferOrder order) {
        return pushTaskQueryService.findByBizAndType(order, List.of(PushTaskType.CARTON_FILE_GENERATION))
                .stream().findFirst()
                .orElseThrow(() -> new RuntimeException("找不到类型=CARTON_FILE_GENERATION的task数据, tsNo=" + order.getOrderNo()));
    }

    @Override
    public InboundRequestHistory addRequestHistory(TransferOrder order, Object request) {
        return inboundRequestHistoryService.add(order, WarehouseProviderType.POLARIS_YUNWMS, null, request);
    }

    @Override
    public InboundRequestHistory updateRequestHistory(InboundRequestHistoryId historyId, Integer responseCode,
                                                      Object responseHeaders, Object responseBody) {
        return inboundRequestHistoryService.update(historyId, responseCode, responseHeaders, responseBody);
    }

    @Override
    public void updateOrder(TransferOrder order, String transferNo) {
        order.setShipmentId(transferNo);
        // 需要实现TransferOrder的保存逻辑
        order.setSyncApiStatus(SyncApiStatus.SUCCESS);
        transferOrderRepository.update(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCartonLabelFiles(TransferOrder order, List<String> fileIds) {
        order.setCartonLabelFileIds(fileIds);
        order.setSyncApiStatus(SyncApiStatus.SUCCESS);
        order.setStatus(TransferOrderStatus.WAIT_CREATE_SHIPMENT);
        //todo 记录TS状态
        transferOrderRepository.update(order);
    }

    @Override
    public void sendCartonFilesMessage(TransferOrder order) {
        //不需要推送箱唛到FMS
    }

    @Override
    public String getOrderNo(TransferOrder order) {
        return order.getTsNo();
    }

    @Override
    public TransferOrderCancelRequest buildCancelTransferOrderRequest(TransferOrder order) {
        TransferOrderCancelRequest req = new TransferOrderCancelRequest();
        req.setBusinessNo(order.getShipmentId());
        return req;
    }

    @Override
    public PushTask createCancelTask(TransferOrder order) {
        return pushTaskService.cancel(order, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    public void clearShipmentId(TransferOrder order) {
        transferOrderService.clearShipmentIdById(order.getId());
    }

    @Override
    public GetTransferOrderInfoRequest buildGetTransferOrderInfoRequest(TransferOrder order) {
        GetTransferOrderInfoRequest req = new GetTransferOrderInfoRequest();
        req.setTransferNo(order.getShipmentId());
        return req;
    }

    @Override
    public PushTask createInboundPushTask(TransferOrder order) {
        return pushTaskService.inbound(order, PushTaskStatus.PENDING, 0, null);
    }

    @Override
    public void handleTransferOrderData(GetTransferOrderInfoResponse data, TransferOrder order, InboundRequestHistoryId historyId) {
        transferOrderService.handleTransferOrderData(data, order, historyId);
    }
}
