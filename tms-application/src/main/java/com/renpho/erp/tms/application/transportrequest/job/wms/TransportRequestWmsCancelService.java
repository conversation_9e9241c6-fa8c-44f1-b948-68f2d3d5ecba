package com.renpho.erp.tms.application.transportrequest.job.wms;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.application.transportrequest.TransportRequestQueryService;
import com.renpho.erp.tms.application.transportrequest.job.common.TransportRequestCommonService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportorder.dto.TransportRequestOrderData;
import com.renpho.erp.tms.domain.transportrequest.*;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.po.converter.TransportRequestConverter;
import com.renpho.erp.tms.infrastructure.persistence.transportrequest.repository.TransportRequestOrderRepository;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.in.IaInfoDto;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsInboundFeign;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TR-目的仓为WMS-取消入库单任务.
 *
 * <AUTHOR>
 * @since 2025/7/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportRequestWmsCancelService {

    private final TransportRequestQueryService transportRequestQueryService;
    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransportRequestCommonService transportRequestCommonService;
    private final PushTaskService pushTaskService;

    private final PushTaskRepository pushTaskRepository;
    private final TransportRequestOrderRepository transportRequestOrderRepository;
    private final PushTaskLookup pushTaskLookup;

    private final TransportRequestConverter transportRequestConverter;

    private final WmsInboundFeign wmsInboundFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    @Transactional(rollbackFor = Exception.class)
//    public void test() {
//        // createInboundWmsTask();
//        // 清除ShipmentId信息
//        //transportRequestOrderRepository.clearShipmentIdById(28);
//        //log.info("清除ShipmentId信息");
//        // 更新同步状态
//        //transportRequestOrderRepository.updateSyncApiStatusById(383, SyncApiStatus.FAIL);
//        // 记录日志
//        //.info("更新同步状态");
//    }

    /**
     * TR-目的仓为WMS-取消入库单任务.
     */
    @Lock4j(name = "transport:request:wms:inbound:cancel:basic")
    //@Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.CANCEL_OPERATOR, desc =
            LogModule.CommonDesc.CANCEL_DESC)
    public void createInboundWmsTask(List<String> trNoList) {
        try {
            log.info("TR-目的仓为WMS的定时器任务开始");

            // 状态是已作废
            TransportRequestStatus status = TransportRequestStatus.CANCEL;
            List<TransportRequestOrderData> trDataList = transportRequestCommonService.selectTrCancelListByStatus(status.getValue(), warehouseType);

            // 外部参数
            trDataList = transportRequestCommonService.fillExterWithTrStatus(status, trDataList, trNoList);

            // 生成入库单推送任务
            if (trDataList != null && !trDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransportRequest> trMap = transportRequestQueryService.getTrMapByIds(trDataList.stream().map(TransportRequestOrderData::getId).toList());

                for (TransportRequestOrderData trData : trDataList) {
                    try {
                        TransportRequest tr = trMap.get(trData.getId());
                        if (tr == null) {
                            log.error("TR-目的仓为WMS-取消入库单任务生成异常, tr上下文找不到, trId={}", trData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != tr.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.cancel(tr, PushTaskStatus.PENDING, 0, null);

                        // 执行取消
                        TransportRequest oldData = pushTaskService.execute(() -> this.cancel(tr, trPushTask), trPushTask);

                        // 记录日志
                        if (oldData != null) {
                            LogRecordContextHolder.putRecordData(String.valueOf(tr.getId().id()), oldData, tr);
                        }
                    } catch (Exception e) {
                        log.error("TR-目的仓为WMS的定时器任务异常, 入库单推送任务生成失败, trId={}", trData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TR-目的仓为WMS的定时器任务异常", e);
        }
    }

    private TransportRequest cancel(TransportRequest tr, PushTask trPushTask) {
        String token = transportRequestCommonService.getWmsAuthToken(tr.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        IaInfoDto cancelVo = new IaInfoDto();
        cancelVo.setIaNo(tr.getShipmentId());

        TransportRequest oldData = transportRequestConverter.trToCopy(tr);
        log.info("TR-目的仓为WMS的取消任务-请求参数, 取消入库单, 取消入库单编号={}", cancelVo.getIaNo());
        R<Boolean> result = wmsInboundFeign.cancel(fromIn, token, cancelVo);
        log.info("TR-目的仓为WMS的取消任务-响应参数, 取消入库单, 取消入库单编号={}, 响应结果={}", cancelVo.getIaNo(), result);
        if (result != null) {
            if (result.isSuccess()) {
                // 记录请求历史
                inboundRequestHistoryService.add(tr, WarehouseProviderType.WMS, null, cancelVo, 200, null, result);

                // 清除ShipmentId信息
                transportRequestOrderRepository.clearShipmentIdById(tr.getId().id());

                // 更新同步状态
                transportRequestOrderRepository.updateSyncApiStatusById(tr.getId().id(), SyncApiStatus.SUCCESS);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(tr, WarehouseProviderType.WMS, null, cancelVo, 200, null, result);
                throw new DingTalkWarning("API取消入库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return oldData;
    }

}
