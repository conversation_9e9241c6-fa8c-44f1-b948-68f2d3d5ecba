package com.renpho.erp.tms.application.transferorder.job.wms;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.tms.application.inbound.InboundRequestHistoryService;
import com.renpho.erp.tms.application.transferorder.TransferOrderService;
import com.renpho.erp.tms.application.transferorder.job.common.TransferOrderCommonService;
import com.renpho.erp.tms.application.transportrequest.PushTaskService;
import com.renpho.erp.tms.domain.exception.DingTalkWarning;
import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transferorder.*;
import com.renpho.erp.tms.domain.transportrequest.PushTask;
import com.renpho.erp.tms.domain.transportrequest.PushTaskStatus;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.erp.tms.infrastructure.common.oplog.LogModule;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.repository.TransferOrderItemRepository;
import com.renpho.erp.tms.infrastructure.remote.wms.dto.outbound.OutBoundCancelMainDto;
import com.renpho.erp.tms.infrastructure.remote.wms.feign.WmsOutboundFeign;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * TS-目的仓为WMS-取消出库单任务.
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransferOrderOutWmsCancelService {

    private final InboundRequestHistoryService inboundRequestHistoryService;
    private final TransferOrderCommonService transferOrderCommonService;
    private final TransferOrderService transferOrderService;
    private final PushTaskService pushTaskService;

    private final TransferOrderRepository transferOrderRepository;
    private final TransferOrderItemRepository transferOrderItemRepository;
    private final TransferOrderConverter transferOrderConverter;

    private final WmsOutboundFeign wmsOutboundFeign;

    // 仓库类型
    private final WarehouseProviderType warehouseType = WarehouseProviderType.WMS;

//    @EventListener(ApplicationReadyEvent.class)
//    @Transactional(rollbackFor = Exception.class)
//    public void test() {
//        // createInboundWmsTask();
//        // 清除ShipmentId信息
//        //transportRequestOrderRepository.clearShipmentIdById(28);
//        //log.info("清除ShipmentId信息");
//        // 更新同步状态
//        //transportRequestOrderRepository.updateSyncApiStatusById(383, SyncApiStatus.FAIL);
//        // 记录日志
//        //.info("更新同步状态");
//    }

    /**
     * TR-目的仓为WMS-取消出库单任务.
     */
    @Lock4j(name = "transfer:request:wms:outbound:cancel:basic")
    //@Transactional(rollbackFor = Exception.class)
    @LogRecord(module = LogModule.TRANSPORT_REQUEST, type = LogModule.CommonDesc.CANCEL_OPERATOR, desc =
            LogModule.CommonDesc.CANCEL_DESC)
    public void createInboundWmsTask(List<String> tsNoList) {
        try {
            log.info("TS-目的仓为WMS的定时器任务开始");

            // 状态是已作废
            TransferOrderStatus status = TransferOrderStatus.CANCEL;
            List<TransferOrderData> tsDataList = transferOrderCommonService.selectTsListByStatus(status.getValue(), warehouseType);

            // 外部参数
            tsDataList = transferOrderCommonService.fillExterWithTsStatus(status, tsDataList, tsNoList);

            // 生成出库单推送任务
            if (tsDataList != null && !tsDataList.isEmpty()) {
                // 获取tr上下文信息
                Map<Integer, TransferOrder> tsMap = transferOrderService.getTsMapByIds(tsDataList.stream().map(TransferOrderData::getId).toList());

                for (TransferOrderData tsData : tsDataList) {
                    try {
                        TransferOrder ts = tsMap.get(tsData.getId());
                        if (ts == null) {
                            log.error("TS-目的仓为WMS-取消出库单任务生成异常, ts上下文找不到, tsId={}", tsData.getId());
                            continue;
                        }
                        // 目的仓限制
                        if (warehouseType != ts.findWarehouseProviderType()) {
                            continue;
                        }

                        // 生成推送任务
                        PushTask trPushTask = pushTaskService.cancel(ts, PushTaskStatus.PENDING, 0, null);

                        // 执行取消
                        TransferOrder oldData = pushTaskService.execute(() -> this.cancel(ts), trPushTask);

                        // 记录日志
                        if (oldData != null) {
                            LogRecordContextHolder.putRecordData(String.valueOf(ts.getId().id()), oldData, ts);
                        }
                    } catch (Exception e) {
                        log.error("TS-目的仓为WMS的定时器任务异常, 出库单推送任务生成失败, tsId={}", tsData.getId(), e);
                    }
                }
            }

        } catch (Exception e) {
            log.error("TS-目的仓为WMS的定时器任务异常", e);
        }
    }

    private TransferOrder cancel(TransferOrder ts) {
        String token = transferOrderCommonService.getWmsAuthToken(ts.getDestWarehouse().getId().id());
        String fromIn = "Y"; // 内部请求,避开erp认证拦截器

        OutBoundCancelMainDto cancelVo = new OutBoundCancelMainDto();
        for(TransferOrderItem tsItem : ts.getItems()){
            cancelVo.addOutboundNo(tsItem.getOutboundNo());
        }

        TransferOrder oldData = transferOrderConverter.tsToCopy(ts);
        log.info("TS-目的仓为WMS的取消任务-请求参数, 取消出库单, 取消出库单编号={}", JSON.toJSONString(cancelVo));
        R<Boolean> result = wmsOutboundFeign.cancelOutboundOrder(fromIn, token, cancelVo);
        log.info("TS-目的仓为WMS的取消任务-响应参数, 取消出库单, 响应结果={}", JSON.toJSONString(result));
        if (result != null) {
            if (result.isSuccess() && result.getData()) {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, cancelVo, 200, null, result);

                // 清除OutboundNo信息,并更新状态
                transferOrderItemRepository.clearOutboundNoByTsId(ts.getId());
                transferOrderRepository.updateSyncApiStatusById(ts.getId(), SyncApiStatus.SUCCESS);
            } else {
                // 记录请求历史
                inboundRequestHistoryService.add(ts, WarehouseProviderType.WMS, null, cancelVo, 200, null, result);
                throw new DingTalkWarning("TS取消出库单失败，原因：%s".formatted(result.getMessage()));
            }
        }
        return oldData;
    }

}
