package com.renpho.erp.tms.client.transferorder;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.karma.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 创建调拨单参数
 *
 * <AUTHOR>
 * @since 2025/8/27
 */
@Getter
@Setter
public class AddTsCmd extends Command {

    @Serial
    private static final long serialVersionUID = -7258803314531433778L;

    @NotBlank
    private String dataSource;

    /**
     * OMS的单号
     */
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    private String refNo;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    @NotNull
    private TransferOrderBizType businessType;

    /**
     * 贸易条款, 字典: trade_terms
     */
    private String tradeTerms;

    /**
     * 付款条款, 字典: payment_terms
     */
    private String paymentTerms;

    /**
     * 店铺ID, 通过店铺ID查询店铺所属渠道以及货主
     */
    @NotNull
    private Integer storeId;

    @JsonIgnore
    private Store store;

    /**
     * 发货仓ID
     */
    @NotNull
    private Integer shippingWarehouseId;

    /**
     * 发货仓Code
     */
    @NotBlank
    private String shippingWarehouseCode;

    @JsonIgnore
    private Warehouse shippingWarehouse;

    /**
     * 目的国/地区
     */
    @NotBlank
    private String destCountryCode;

    @JsonIgnore
    private CountryRegion destCountryRegion;

    /**
     * 目的仓库ID
     */
    @NotNull
    private Integer destWarehouseId;

    /**
     * 目的仓Code
     */
    @NotBlank
    private String destWarehouseCode;

    @JsonIgnore
    private Warehouse destWarehouse;

    /**
     * 目的地
     */
    @NotBlank
    private String destAddress;

    /**
     * 调拨原因
     */
    @NotNull
    private TransferReason transferReason;

    /**
     * 调拨原因描述
     */
    private String transferReasonDesc;

    /**
     * 是否打托
     */
    @NotNull
    private Boolean isPalletized;

    /**
     * 预估发货时间
     */
    @NotNull
    private LocalDateTime estimatedDepartureTime;

    /**
     * 预估交货时间
     */
    @NotNull
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 期望上架时间
     */
    @NotNull
    private LocalDateTime expectedPutawayTime;

    /**
     * 提单类型, 字典: bill_of_lading_type
     */
    private String billOfLadingType;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 允许发货, 0-不允许, 1-允许
     */
    private Boolean isAllowedShipping;

    /**
     * 批注
     */
    private String comment;

    /**
     * 调拨单明细
     */
    @NotEmpty
    private List<@NotNull AddItemCmd> items;

    /**
     * 运营
     */
    private Integer salesStaffId;

    /**
     * 创建人, 同时赋值到创建人和计划人员
     */
    private Integer planerStaffId;

    /**
     * 客户信息
     */
    private AddCustomerCmd customer;

}
