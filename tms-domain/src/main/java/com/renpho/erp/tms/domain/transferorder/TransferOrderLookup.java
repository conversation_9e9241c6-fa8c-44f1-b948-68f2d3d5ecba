package com.renpho.erp.tms.domain.transferorder;

import com.renpho.erp.tms.domain.inbound.WarehouseProviderType;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.karma.dto.Paging;
import org.jmolecules.ddd.integration.AssociationResolver;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 调拨单查询接口
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
public interface TransferOrderLookup extends AssociationResolver<TransferOrder, TransferOrderId> {

    Paging<TransferOrder> findPage(TransferOrderQuery condition);

    List<TransferOrder> findList(TransferOrderQuery condition);

    /**
     * 根据调拨单号查询
     *
     * @param tsNo 调拨单号
     * @return 调拨单
     */
    Optional<TransferOrder> findByTsNo(String tsNo);

    /**
     * 根据OMS单号查询
     *
     * @param orderNo OMS单号
     * @return 调拨单列表
     */
    List<TransferOrder> findByOrderNo(String orderNo);

    /**
     * 根据状态查询
     *
     * @param status 状态
     * @return 调拨单列表
     */
    List<TransferOrder> findByStatus(TransferOrderStatus status);

    /**
     * 根据tr单状态查询待补充状态的tr单-运单号
     *
     * @param status        TS单状态,字典: transfer_order_status,可选值: 0-待补充信息 1-待拼柜 2-已拼柜 3-已作废
     * @param warehouseType 推送类型, WMS,京东,极智佳,乐鱼,KingSpark,FBA,AWD,FBT,WFS,手动签收）
     * @return 待补充状态的ts单-运单号
     */
    List<TransferOrderData> selectTsListByStatus(Integer status, WarehouseProviderType warehouseType);

    /**
     * 根据TS单号列表查询TS单信息
     *
     * @param needTsNoList TS单号列表
     * @return TS单信息
     */
    List<TransferOrderData> selectTsListByTsNoList(List<String> needTsNoList);

    /**
     * 根据TS ID列表查询TS单信息
     *
     * @param tsIds TS ID列表
     * @return TS单信息
     */
    List<TransferOrder> findByIds(List<TransferOrderId> tsIds);


    /**
     * 根据推送类型、TS ID列表、TS单号列表、TS单状态、推送状态查询TS单信息
     * @param warehouseProviderType 推送类型, WMS,京东,极智佳,乐鱼,KingSpark,FBA,AWD,FBT,WFS,手动签收）
     * @param tsIds TS ID列表
     * @param tsNos TS单号列表
     * @param transferOrderStatuses TS单状态,字典: transfer_order_status,可选值: 0-待补充信息 1-待拼柜 2-已拼柜 3-已作废
     * @param apiStatus 推送状态
     * @return TS单信息
     */
    List<TransferOrder> findByWarehouseProviderType(WarehouseProviderType warehouseProviderType,
                                                    Collection<TransferOrderId> tsIds,
                                                    Collection<String> tsNos,
                                                    Collection<TransferOrderStatus> transferOrderStatuses,
                                                    SyncApiStatus apiStatus);

    List<TransferOrder> findOperatorAssociations(Collection<TransferOrder> domains);

    List<TransferOrder> findCountryRegionAssociations(Collection<TransferOrder> domains);

    List<TransferOrder> findSalesChannelAssociations(Collection<TransferOrder> domains);

    List<TransferOrder> findOwnerAssociations(Collection<TransferOrder> domains);

    List<TransferOrder> findWarehouseAssociations(Collection<TransferOrder> domains);

    List<TransferOrder> findStoreAssociations(Collection<TransferOrder> domains);

    /**
     * 统计调拨单状态
     * <AUTHOR>
     * @date 2025/8/26 16:29
     * @param
     */
    Map<Integer, Object> countByStatus();
}
