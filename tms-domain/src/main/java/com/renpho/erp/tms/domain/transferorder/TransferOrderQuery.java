package com.renpho.erp.tms.domain.transferorder;

import com.renpho.karma.dto.PageQuery;
import lombok.Getter;
import lombok.Setter;
import org.jmolecules.architecture.cqrs.QueryModel;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@QueryModel
@Getter
@Setter
public class TransferOrderQuery extends PageQuery {
    @Serial
    private static final long serialVersionUID = 8336228470038073040L;

    /**
     * 主表id
     */
    private List<Integer> ids;

    /**
     * 调拨单单号
     */
    private String tsNo;

    /**
     * 发货仓
     */
    private Integer shippingWarehouseId;

    /**
     * 目的仓库ID
     */
    private Integer destWarehouseId;

    /**
     * 调拨单类型, 字典: transfer_order_type
     */
    private List<String> type;

    /**
     * PSKU
     */
    private String psku;

    /**
     * FNSKU
     */
    private String fnsku;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    private String tsBizType;

    /**
     * 货主ID
     */
    private Integer ownerId;

    /**
     * 店铺ID
     */
    private List<Integer> storeIds;

    /**
     * 销售渠道ID
     */
    private Integer salesChannelId;

    /**
     * 目的国/地区
     */
    private List<String> destCountryCode;

    /**
     * 发货Id
     */
    private String shipmentId;

    /**
     * 出库单号
     */
    private String outboundNo;

    /**
     * OMS的单号
     */
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    private String refNo;

    /**
     * 允许发货, 0-不允许, 1-允许
     */
    private Boolean isAllowedShipping;

    /**
     * 是否借货, 0-不允许, 1-允许
     */
    private Boolean isBorrowed;

    /**
     * 是否换标, 0-不允许, 1-允许
     */
    private Boolean isRelabel;

    /**
     * 预估发货开始时间
     */
    private LocalDateTime estimatedDepartureStartTime;

    /**
     * 预估发货结束时间
     */
    private LocalDateTime estimatedDepartureEndTime;

    /**
     * 预估交货开始时间
     */
    private LocalDateTime estimatedDeliveryStartTime;

    /**
     * 预估交货结束时间
     */
    private LocalDateTime estimatedDeliveryEndTime;

}
