package com.renpho.erp.tms.adapter.web.job.transferorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.tms.application.transferorder.TransferOrderFBAInboundService;
import com.renpho.erp.tms.application.transferorder.walmart.TransferOrderWalmartInboundService;
import com.renpho.erp.tms.domain.transferorder.TransferOrderId;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 调拨单中目的仓类型为平台仓的拉取入库单定时任务
 *
 * <AUTHOR>
 * @since 2025/8/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransferOrderPlatformWarehouseJob {

    private final TransferOrderFBAInboundService transferOrderFBAInboundService;
    private final TransferOrderWalmartInboundService transferOrderWalmartInboundService;


    /**
     * 拉取调拨单走FBA的入库信息
     */
    @XxlJob("pullShipmentsFromFBAForTS")
    public void pullShipmentsFromFBAForTS() {
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        String syncApiStatus = Optional.ofNullable(jo.getString("syncApiStatus")).orElse(SyncApiStatus.SUCCESS.getValue());
        List<String> tsNos = jo.getJSONArray("tsNos").toJavaList(String.class);
        List<Integer> tsIds = jo.getJSONArray("tsIds").toJavaList(Integer.class);
        List<TransferOrderId> ids = CollectionUtils.emptyIfNull(tsIds).stream().map(TransferOrderId::new).toList();


        transferOrderFBAInboundService.pullShipmentsFromFBAForTS(tsNos, ids, syncApiStatus);
    }



    /**
     * 拉取调拨单走WFS的入库信息
     */
    @XxlJob("pullShipmentsFromWalmartForTS")
    public void pullShipmentsFromWalmartForTS() {
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jo = JSON.parseObject(jobParam);
        String syncApiStatus = Optional.ofNullable(jo.getString("syncApiStatus")).orElse(SyncApiStatus.SUCCESS.getValue());
        List<String> tsNos = jo.getJSONArray("tsNos").toJavaList(String.class);
        List<Integer> tsIds = jo.getJSONArray("tsIds").toJavaList(Integer.class);
        List<TransferOrderId> ids = CollectionUtils.emptyIfNull(tsIds).stream().map(TransferOrderId::new).toList();


        transferOrderWalmartInboundService.pullShipmentsFromWalmartForTS(tsNos, ids, syncApiStatus);
    }


}
