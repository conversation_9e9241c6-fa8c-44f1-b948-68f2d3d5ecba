package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.validator;

import com.renpho.erp.oms.client.skuMapping.command.query.GetListingMappingMskuBySku;
import com.renpho.erp.tms.adapter.web.controller.command.validator.ItemsExist;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.AddCmd;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.AddItemCmd;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.infrastructure.remote.product.repository.ProductLookup;
import com.renpho.erp.tms.infrastructure.remote.skumapping.repository.SkuMappingLookup;
import groovy.lang.Tuple;
import groovy.lang.Tuple2;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.lang.annotation.Annotation;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

/**
 * 调拨单校验器
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
public abstract class TransferOrderValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected BiPredicate<T, ConstraintValidatorContext> predicate;

    @Resource
    protected ProductLookup productLookup;

    @Resource
    protected SkuMappingLookup skuMappingLookup;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        if (productLookup == null) {
            return true;
        }
        return predicate.test(value, context);
    }

    public static class IsItemsExist extends TransferOrderValidator<ItemsExist, AddCmd> {

        @Override
        public void initialize(ItemsExist constraintAnnotation) {
            // 保留原有的predicate逻辑作为备用
            predicate = (cmd, context) -> {
                if (cmd == null) {
                    return true;
                }
                List<AddItemCmd> items = cmd.getItems();
                if (CollectionUtils.isEmpty(items)) {
                    return true;
                }
                // 判断店铺是否存在
                if (cmd.getStoreId() == null) {
                    return true;
                }

                Set<String> pskus = new HashSet<>();
                List<GetListingMappingMskuBySku> params = new ArrayList<>();
                Set<Tuple2<String, String>> tuples = new HashSet<>();
                for (AddItemCmd item : items) {
                    pskus.add(item.getPsku());
                    params.add(getSkuMapping(cmd, item));
                    if (StringUtils.isNoneBlank(item.getBorrowedPsku(), item.getBorrowedFnsku())) {
                        tuples.add(Tuple.tuple(item.getBorrowedPsku(), item.getBorrowedFnsku()));
                        params.add(getSkuMapping(cmd, item));
                    }
                    tuples.add(Tuple.tuple(item.getPsku(), item.getFnsku()));
                }
                // 判断 psku 是否存在
                Map<String, Product> products = productLookup.findByPskus(pskus);
                if (MapUtils.isEmpty(products)) {
                    return false;
                }
                if (!products.keySet().containsAll(pskus)) {
                    return false;
                }
                cmd.setProducts(products);

                // 判断 psku 与 fnsku 是否匹配
                Set<Tuple2<String, String>> mappings = skuMappingLookup.findList(params).stream()
                        .filter(Objects::nonNull)
                        .map(m -> Tuple.tuple(m.getPsku(), m.getFnsKu()))
                        .collect(Collectors.toSet());
                return mappings.containsAll(tuples);
            };
        }

        @NotNull
        private static GetListingMappingMskuBySku getSkuMapping(AddCmd cmd, AddItemCmd item) {
            GetListingMappingMskuBySku param = new GetListingMappingMskuBySku();
            param.setStoreId(cmd.getStoreId());
            param.setPsku(item.getPsku());
            param.setFnsKu(item.getFnsku());
            return param;
        }

    }
}
