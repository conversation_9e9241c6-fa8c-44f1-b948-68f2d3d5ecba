package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.renpho.erp.tms.adapter.web.controller.command.container.*;
import com.renpho.erp.tms.domain.product.Product;
import com.renpho.erp.tms.domain.regiontimezone.CountryTimeZone;
import com.renpho.erp.tms.domain.site.CountryRegion;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.store.StoreId;
import com.renpho.erp.tms.client.transferorder.TransferOrderBizType;
import com.renpho.erp.tms.domain.transferorder.TransferOrderDataSource;
import com.renpho.erp.tms.domain.transferorder.TransferReason;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.karma.dto.Command;
import groovy.lang.Tuple;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 创建调拨单入参
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Getter
@Setter
public class AddCmd extends Command implements Serializable, StoreIdContainer, StoreIdsContainer, CountryCodeContainer, ItemsContainer<List<AddItemCmd>, AddItemCmd>, WarehouseIdsContainer {

    @Serial
    private static final long serialVersionUID = -2900403144528539L;

    @NotBlank
    private String dataSource;

    /**
     * OMS的单号
     */
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    private String refNo;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    @NotNull
    private TransferOrderBizType businessType;

    /**
     * 贸易条款, 字典: trade_terms
     */
    private String tradeTerms;

    /**
     * 付款条款, 字典: payment_terms
     */
    private String paymentTerms;

    /**
     * 店铺ID, 通过店铺ID查询店铺所属渠道以及货主
     */
    @NotNull
    private Integer storeId;

    @JsonIgnore
    private Store store;

    /**
     * 发货仓ID
     */
    @NotNull
    private Integer shippingWarehouseId;

    /**
     * 发货仓Code
     */
    @NotBlank
    private String shippingWarehouseCode;

    @JsonIgnore
    private Warehouse shippingWarehouse;

    /**
     * 目的国/地区
     */
    @NotBlank
    private String destCountryCode;

    @JsonIgnore
    private CountryRegion destCountryRegion;

    /**
     * 目的仓库ID
     */
    @NotNull
    private Integer destWarehouseId;

    /**
     * 目的仓Code
     */
    @NotBlank
    private String destWarehouseCode;

    @JsonIgnore
    private Warehouse destWarehouse;

    /**
     * 目的地
     */
    @NotBlank
    private String destAddress;

    /**
     * 调拨原因
     */
    @NotNull
    private TransferReason transferReason;

    /**
     * 调拨原因描述
     */
    private String transferReasonDesc;

    /**
     * 是否打托
     */
    @NotNull
    private Boolean isPalletized;

    /**
     * 预估发货时间
     */
    @NotNull
    private LocalDateTime estimatedDepartureTime;

    /**
     * 预估交货时间
     */
    @NotNull
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 期望上架时间
     */
    @NotNull
    private LocalDateTime expectedPutawayTime;

    /**
     * 提单类型, 字典: bill_of_lading_type
     */
    private String billOfLadingType;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 允许发货, 0-不允许, 1-允许
     */
    private Boolean isAllowedShipping;

    /**
     * 批注
     */
    private String comment;

    /**
     * 调拨单明细
     */
    @NotEmpty
    private List<@NotNull AddItemCmd> items;

    /**
     * 运营
     */
    private Integer salesStaffId;

    /**
     * 创建人, 同时赋值到创建人和计划人员
     */
    private Integer planerStaffId;

    /**
     * 客户信息
     */
    private AddCustomerCmd customer;

    @Override
    public String getCountryCode() {
        return destCountryCode;
    }

    @Override
    public void setCountryRegion(CountryRegion countryRegion) {
        setDestCountryRegion(countryRegion);
    }

    @Override
    public void setCountryTimeZone(CountryTimeZone countryTimeZone) {
        Optional.ofNullable(this.destCountryRegion).ifPresent(c -> c.setTimezone(countryTimeZone));
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isTransferReasonIsOtherAndTransferReasonDescIsNotBlank() {
        return !TransferReason.OTHER.equals(this.transferReason) || StringUtils.isNotBlank(transferReasonDesc);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isItemsNotDuplicated() {
        return CollectionUtils.emptyIfNull(items)
                .stream()
                .filter(Objects::nonNull)
                .map(i -> Tuple.tuple(i.getPsku(), i.getFnsku()))
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet()
                .stream()
                .noneMatch(c -> c.getValue() > 1);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isBorrowedAndBorrowedPskuNotBlank() {
        return CollectionUtils.emptyIfNull(items)
                .stream()
                .filter(Objects::nonNull)
                .allMatch(i -> !i.getIsBorrowed() || StringUtils.isNotBlank(i.getBorrowedPsku()));
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isDataSourceNotDefaultAndCreateByNotNull() {
        return TransferOrderDataSource.TMS.name().equals(this.dataSource) || Objects.nonNull(this.planerStaffId);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isDataSourceNotDefaultAndTradeTermsNotBlank() {
        return TransferOrderDataSource.TMS.name().equals(this.dataSource) || StringUtils.isNotBlank(this.tradeTerms);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isDataSourceNotDefaultAndPaymentTermsNotBlank() {
        return TransferOrderDataSource.TMS.name().equals(this.dataSource) || StringUtils.isNotBlank(this.paymentTerms);
    }

    @AssertTrue
    @SuppressWarnings("unused")
    public boolean isDataSourceNotDefaultAndCustomerNotNull() {
        if (TransferOrderDataSource.TMS.name().equals(this.dataSource)) {
            return true;
        }
        return Optional.ofNullable(this.customer).filter(AddCustomerCmd::isValid).isPresent();
    }

    @Override
    public Set<Integer> getStoreIds() {
        return CollectionUtils.emptyIfNull(this.items)
                .stream()
                .filter(Objects::nonNull)
                .map(AddItemCmd::getBorrowedStoreId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    @Override
    public void setStores(Map<StoreId, Store> stores) {
        stores = MapUtils.emptyIfNull(stores);
        for (AddItemCmd item : CollectionUtils.emptyIfNull(this.items)) {
            if (item != null) {
                Optional<Store> store = Optional.ofNullable(item.getBorrowedStoreId()).map(StoreId::new).map(stores::get);
                store.ifPresent(item::setBorrowedStore);
                store.map(Store::getActiveOwner).ifPresent(item::setBorrowedOwner);
            }
        }
    }

    public void setProducts(Map<String, Product> products) {
        products = MapUtils.emptyIfNull(products);
        for (AddItemCmd item : CollectionUtils.emptyIfNull(this.items)) {
            if (item != null) {
                Optional<Product> product = Optional.ofNullable(item.getPsku()).map(products::get);
                product.ifPresent(p -> p.setFnSku(item.getFnsku()));
                product.ifPresent(item::setProduct);
            }
        }
    }

    @Override
    public Set<Integer> getWarehouseIds() {
        return Stream.of(Optional.ofNullable(this.shippingWarehouseId), Optional.ofNullable(this.destWarehouseId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toSet());
    }

    @Override
    public void setWarehouses(Map<WarehouseId, Warehouse> warehouses) {
        warehouses = MapUtils.emptyIfNull(warehouses);
        Optional.ofNullable(this.shippingWarehouseId).map(WarehouseId::new).map(warehouses::get).ifPresent(this::setShippingWarehouse);
        Optional.ofNullable(this.destWarehouseId).map(WarehouseId::new).map(warehouses::get).ifPresent(this::setDestWarehouse);
    }
}
