package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.WarehouseIdsContainer;
import com.renpho.erp.tms.domain.warehouse.Warehouse;
import com.renpho.erp.tms.domain.warehouse.WarehouseId;
import com.renpho.erp.tms.infrastructure.common.converter.BooleanConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.repository.WarehouseLookup;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.collections4.CollectionUtils;

import java.lang.annotation.Annotation;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/26
 */
public abstract class WarehouseValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected BiPredicate<T, ConstraintValidatorContext> predicate;

    @Resource
    protected WarehouseLookup warehouseLookup;

    @Resource
    protected BooleanConverter booleanConverter;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return warehouseLookup == null || predicate.test(value, context);
    }

    public static class WarehouseIdsExist extends WarehouseValidator<WarehousesExist, WarehouseIdsContainer> {
        @Override
        public void initialize(WarehousesExist constraintAnnotation) {
            predicate = (cmd, context) -> {
                Optional<Set<Integer>> warehouseIdsOpt = Optional.ofNullable(cmd).map(WarehouseIdsContainer::getWarehouseIds).filter(CollectionUtils::isNotEmpty);
                if (warehouseIdsOpt.isEmpty()) {
                    return true;
                }

                Set<WarehouseId> warehouseIds = warehouseIdsOpt.get()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(WarehouseId::new)
                        .collect(Collectors.toSet());
                Map<WarehouseId, Warehouse> warehouses = warehouseLookup.findByIds(warehouseIds);
                // 过滤被禁用的仓库
                warehouses.entrySet().removeIf(e -> !booleanConverter.toBoolean(e.getValue().getStatus()));
                // 过滤未审核通过的仓库
                warehouses.entrySet().removeIf(e -> e.getValue().getAuditStatus() != 2);
                if (!warehouses.keySet().containsAll(warehouseIds)) {
                    return false;
                }
                cmd.setWarehouses(warehouses);
                return true;
            };
        }
    }
}
