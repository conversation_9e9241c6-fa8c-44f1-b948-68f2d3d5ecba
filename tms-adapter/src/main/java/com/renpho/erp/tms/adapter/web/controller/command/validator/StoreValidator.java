package com.renpho.erp.tms.adapter.web.controller.command.validator;

import com.renpho.erp.tms.adapter.web.controller.command.container.StoreIdContainer;
import com.renpho.erp.tms.adapter.web.controller.command.container.StoreIdsContainer;
import com.renpho.erp.tms.domain.store.Store;
import com.renpho.erp.tms.domain.store.StoreId;
import com.renpho.erp.tms.infrastructure.common.converter.BooleanConverter;
import com.renpho.erp.tms.infrastructure.remote.store.repository.StoreLookup;
import com.renpho.karma.i18n.I18nMessageKit;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.collections4.CollectionUtils;

import java.lang.annotation.Annotation;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/18
 */
public abstract class StoreValidator<A extends Annotation, T> implements ConstraintValidator<A, T> {

    protected BiPredicate<T, ConstraintValidatorContext> predicate;

    @Resource
    protected StoreLookup storeLookup;

    @Resource
    protected BooleanConverter booleanConverter;

    @Override
    public boolean isValid(T value, ConstraintValidatorContext context) {
        return storeLookup == null || predicate.test(value, context);
    }

    public static class StoreIdExist extends StoreValidator<StoreExist, StoreIdContainer> {

        @Override
        public void initialize(StoreExist constraintAnnotation) {
            predicate = (cmd, context) -> Optional.ofNullable(cmd)
                    .map(StoreIdContainer::getStoreId)
                    .map(StoreId::new)
                    .flatMap(storeLookup::findById)
                    .filter(c -> booleanConverter.toBoolean(c.getStatus()))
                    .isPresent();
        }

    }

    public static class StoreIdsExist extends StoreValidator<StoresExist, StoreIdsContainer> {
        @Override
        public void initialize(StoresExist constraintAnnotation) {
            predicate = (cmd, context) -> {
                Optional<Set<Integer>> optional = Optional.ofNullable(cmd).map(StoreIdsContainer::getStoreIds).filter(CollectionUtils::isNotEmpty);
                if (optional.isEmpty()) {
                    return true;
                }

                Set<StoreId> storeIds = optional.get()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(StoreId::new)
                        .collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(storeIds)) {
                    return true;
                }
                Map<StoreId, Store> stores = storeLookup.findByIds(storeIds);
                if (!stores.keySet().containsAll(storeIds)) {
                    storeIds.removeAll(stores.keySet());
                    // 将缺失的 storeId 放入上下文
                    context.buildConstraintViolationWithTemplate(I18nMessageKit.getMessage("store.not.exist.placeholder", storeIds.stream().map(String::valueOf).collect(Collectors.joining(",")))).addConstraintViolation();
                    return false;
                }
                cmd.setStores(stores);
                return true;
            };
        }
    }
}
