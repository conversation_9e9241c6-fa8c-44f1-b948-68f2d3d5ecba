package com.renpho.erp.tms.adapter.web.controller.transferorder.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.tms.domain.transferorder.TransferOrderDataSource;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Getter
@Setter
public class TransferOrderVO implements Serializable, VO {
    @Serial
    private static final long serialVersionUID = -4625497280728770193L;

    private Integer id;

    /**
     * 调拨单单号
     */
    private String tsNo;

    /**
     * OMS的单号
     */
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    private String refNo;

    /**
     * 差异单号
     */
    private String doNo;

    /**
     * 差异单状态
     */
    @Trans(type = TransType.DICTIONARY, key = "ims_inventory_approval_status", ref = "doStatusName")
    private String doStatus;

    private String doStatusName;

    /**
     * 调拨单状态, 字典: transfer_order_status
     */
    @Trans(type = TransType.DICTIONARY, key = "transfer_order_status", ref = "statusName")
    private Integer status;

    private String statusName;

    /**
     * 调拨单类型, 字典: transfer_order_type
     */
    @Trans(type = TransType.DICTIONARY, key = "transfer_order_type", ref = "typeName")
    private String type;

    private String typeName;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    @Trans(type = TransType.DICTIONARY, key = "transfer_order_biz_type", ref = "tsBizTypeName")
    private String tsBizType;

    private String tsBizTypeName;

    /**
     * 数据来源, 判断是TMS创建还是上游传入
     */
    private TransferOrderDataSource dataSource;

    /**
     * 销售渠道ID
     */
    private Integer salesChannelId;

    /**
     * 销售渠道名称
     */
    private String salesChannelName;

    /**
     * 店铺ID
     */
    private Integer storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 货主ID
     */
    private Integer ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 发货仓
     */
    private String shippingWarehouse;

    /**
     * 目的国/地区
     */
    private String destCountryCode;

    /**
     * 目的仓库ID
     */
    private Integer destWarehouseId;

    /**
     * 目的仓Code
     */
    private String destWarehouseCode;

    /**
     * 目的地
     */
    private String destAddress;

    /**
     * 贸易条款, 字典: trade_terms
     */
    private String tradeTerms;

    /**
     * 付款条款, 字典: payment_terms
     */
    private String paymentTerms;

    /**
     * 是否借货, 0-否, 1-是
     */
    private Boolean isBorrowed;

    /**
     * 是否换标, 0-否, 1-是
     */
    private Boolean isRelabel;

    /**
     * 是否打托
     */
    private Boolean isPalletized;

    /**
     * 预估发货时间
     */
    private LocalDateTime estimatedDepartureTime;

    /**
     * 预估交货时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 预估到达时间
     */
    private LocalDateTime estimatedArrivalTime;

    /**
     * 实际发货时间
     */
    private LocalDateTime actualDepartureTime;

    /**
     * 实际交货时间
     */
    private LocalDateTime actualDeliveryTime;

    /**
     * 期望上架时间
     */
    private LocalDateTime expectedPutawayTime;

    /**
     * 总数量
     */
    private Integer qty;

    /**
     * 总箱数
     */
    private BigDecimal boxQty;

    /**
     * 总毛重
     */
    private BigDecimal grossWeight;

    /**
     * 总净重
     */
    private BigDecimal netWeight;

    /**
     * 总体积
     */
    private BigDecimal volume;

    /**
     * 提单类型, 字典: bill_of_lading_type
     */
    private String billOfLadingType;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 允许发货, 0-不允许, 1-允许
     */
    private Boolean isAllowedShipping;

    /**
     * 关闭时间
     */
    private LocalDateTime closedTime;

    /**
     * 运营人员ID
     */
    private Integer salesStaffId;

    /**
     * 运营人员名称
     */
    private String salesStaffName;

    /**
     * 计划人员ID
     */
    private Integer planerStaffId;

    /**
     * 计划人员名称
     */
    private String planerStaffName;

    /**
     * 船务人员ID
     */
    private Integer shippingStaffId;

    /**
     * 船务人员名称
     */
    private String shippingStaffName;

    /**
     * 创建人 ID
     */
    private Integer createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建人工号
     */
    private String createByCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人 ID
     */
    private Integer updateBy;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新人工号
     */
    private String updateByCode;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后一次失败信息
     */
    private List<String> lastErrMsg;

    /**
     * 调拨单商品列表
     */
    private List<TransferOrderItemVO> items;

    /**
     * 调拨单客户信息
     */
    private TransferOrderCustomerVO customer;

    /**
     * 调拨单 inbound 信息
     */
    private TransferOrderInboundVO inbound;

    /**
     * 调拨单 outbound 信息
     */
    private TransferOrderOutboundVO outbound;
}
