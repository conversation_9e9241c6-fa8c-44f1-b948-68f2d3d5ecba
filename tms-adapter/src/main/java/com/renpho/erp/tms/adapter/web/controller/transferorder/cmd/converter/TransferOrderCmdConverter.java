package com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.converter;

import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.AddCmd;
import com.renpho.erp.tms.adapter.web.controller.transferorder.cmd.UpdateCmd;
import com.renpho.erp.tms.domain.transferorder.TransferOrder;
import com.renpho.erp.tms.infrastructure.persistence.transferorder.po.converter.TransferOrderConverter;
import com.renpho.erp.tms.infrastructure.remote.owner.converter.OwnerConverter;
import com.renpho.erp.tms.infrastructure.remote.saleschannel.converter.SalesChannelConverter;
import com.renpho.erp.tms.infrastructure.remote.store.converter.StoreConverter;
import com.renpho.erp.tms.infrastructure.remote.user.converter.OperatorConverter;
import com.renpho.erp.tms.infrastructure.remote.warehouse.converter.WarehouseConverter;
import org.mapstruct.*;

import java.util.Optional;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/8/23
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TransferOrderItemCmdConverter.class, TransferOrderCustomerCmdConverter.class, TransferOrderConverter.class, SalesChannelConverter.class, StoreConverter.class, OwnerConverter.class, TransferOrderConverter.class, WarehouseConverter.class, OperatorConverter.class},
        imports = {Optional.class}
)
public interface TransferOrderCmdConverter {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tsNo", ignore = true)
    @Mapping(target = "items", source = "items")
    @Mapping(target = "created.operatorId.id", source = "planerStaffId")
    @Mapping(target = "updated.operatorId.id", source = "planerStaffId")
    @Mapping(target = "planerStaffId.id", source = "planerStaffId")
    @Mapping(target = "planerStaff.operatorId.id", source = "planerStaffId")
    @Mapping(target = "salesChannelId.id", source = "store.salesChannelId")
    @Mapping(target = "salesStaffId.id", source = "salesStaffId")
    @Mapping(target = "storeId", source = "store.id")
    @Mapping(target = "store", source = "store")
    @Mapping(target = "ownerId", source = "store.activeOwner.id")
    @Mapping(target = "owner", source = "store.activeOwner")
    @Mapping(target = "shippingWarehouseId", source = "shippingWarehouseId")
    @Mapping(target = "shippingWarehouse.id", source = "shippingWarehouseId")
    @Mapping(target = "shippingWarehouse.code", source = "shippingWarehouseCode")
    @Mapping(target = "destWarehouseId", source = "destWarehouseId")
    @Mapping(target = "destWarehouse.id", source = "destWarehouseId")
    @Mapping(target = "destWarehouse.code", source = "destWarehouseCode")
    TransferOrder toDomain(AddCmd cmd);

    @InheritConfiguration(name = "toDomain")
    @Mapping(target = "id", source = "id")
    @Mapping(target = "created", ignore = true)
    @Mapping(target = "updated.operatorId", source = "updateBy")
    TransferOrder toDomain(UpdateCmd cmd);

}

